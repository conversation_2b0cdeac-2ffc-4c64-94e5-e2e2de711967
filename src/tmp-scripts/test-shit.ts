import "./dependencies";
import ScriptRunner from "../jobs/services/scriptRunner";
import LearnNewsImageFinder from "../lib/learnNewsImageFinder";

class TestShit extends ScriptRunner {
  scriptName = "test-shit";

  async processFn(): Promise<void> {
    const imageUrl = await LearnNewsImageFinder.find(
      "Oil Pauses Rally as Inventories Rise and Geopolitical Moves Grab Attention"
    );
    console.log(imageUrl);
  }
}

new TestShit().run();
