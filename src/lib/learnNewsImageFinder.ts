import { generateObject, generateText } from "ai";
import { anthropic } from "@ai-sdk/anthropic";
import { z } from "zod";
import { captureException } from "@sentry/node";
import logger from "../external-services/loggerService";
import { JSD<PERSON> } from "jsdom";
import { delay } from "../utils/scriptUtil";
import { google } from "@ai-sdk/google";
import { openai } from "@ai-sdk/openai";

const ANTHROPIC_MODEL_NAME = "claude-sonnet-4-20250514";

const LEARN_NEWS_IMAGE_FINDER_ARTICLES_OUTPUT_SCHEMA = z.object({
  articles: z.array(z.string()).describe("Array of Article URLs")
});

const LEARN_NEWS_IMAGE_FINDER_IMAGE_OUTPUT_SCHEMA = z.object({
  imageUrl: z.string().describe("Selected Image URL")
});

export default class LearnNewsImageFinder {
  public static async find(newsArticleTitle: string): Promise<string> {
    try {
      const articleUrls = await this._getRelatedArticleUrls(newsArticleTitle);
      if (articleUrls.length === 0) {
        return undefined;
      }

      return;

      console.log(articleUrls);
      await delay(10000);

      const imageUrls = await this._extractImagesFromArticles(articleUrls);
      if (imageUrls.length === 0) {
        return undefined;
      }

      console.log(imageUrls);
      await delay(10000);

      return await this._selectMostRelevantImage(newsArticleTitle, imageUrls);
    } catch (err) {
      captureException(err);
      logger.error("Error finding news article image", {
        module: "LearnNewsImageFinder",
        method: "find",
        data: { content: newsArticleTitle, err }
      });
      return undefined;
    }
  }

  private static async _getRelatedArticleUrls(newsArticleTitle: string): Promise<string[]> {
    const prompt = `I want you to search the web for the top 5 articles related to the news titled "${newsArticleTitle}".

Once you find those top 5 articles i want you to search for their article header image (it's probably going to be the og:image). Once once you find all the corresponding images (or as many as you can - some articles may not have an image), i want you to provide me with a response that is the best image out of those articles that is best suited for the title i gave you.

If you cannot find an image for those articles, then start looking for articles related to the market of the topic and repeat the process. So find the top 5 articles for the market and pick up the best image.

The result of your response should be an image url.`;

    const result = await generateText({
      model: openai.responses("gpt-4o-mini"),
      prompt: prompt,
      // Force web search tool:
      tools: {
        web_search_preview: openai.tools.webSearchPreview()
      },
      toolChoice: { type: "tool", toolName: "web_search_preview" }
    });

    console.log(result);

    // console.log(providerMetadata?.google?.groundingMetadata);

    // console.log(text);

    return ["text"]; // Extract the array from the object
  }

  private static async _extractImagesFromArticles(articleUrls: string[]): Promise<string[]> {
    const articlesHtml = await Promise.all(
      articleUrls.map(async (url) => {
        try {
          const response = await fetch(url, { signal: AbortSignal.timeout(20000) });
          if (!response.ok) {
            console.warn(`Failed to fetch ${url}: ${response.status}`);
            return null;
          }
          return await response.text();
        } catch (error) {
          console.warn(`Error fetching ${url}:`, error.message);
          return null;
        }
      })
    );

    return articlesHtml
      .filter(Boolean)
      .map((html) => {
        const dom = new JSDOM(html);
        const document = dom.window.document;
        const image = document.querySelector("meta[property='og:image']")?.getAttribute("content");
        return image;
      })
      .filter(Boolean);
  }

  private static async _selectMostRelevantImage(newsArticleTitle: string, imageUrls: string[]): Promise<string> {
    const prompt = `I want you to evaluate which of the following pictures would be the most relevant for a news article titled "${newsArticleTitle}" and return its URL. Exclude pictures that are watermarked.
    
Available images: ${imageUrls.join(", ")}

Return a JSON object with the selected "imageUrl".`;

    const { object } = await generateObject({
      model: anthropic(ANTHROPIC_MODEL_NAME),
      prompt,
      schema: LEARN_NEWS_IMAGE_FINDER_IMAGE_OUTPUT_SCHEMA,
      temperature: 0.2
    });

    return object.imageUrl; // Extract the URL from the object
  }
}
