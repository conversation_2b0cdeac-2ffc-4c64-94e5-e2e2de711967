import { captureException } from "@sentry/node";
import { BankAccountsFilter } from "filters";
import mongoose, { QueryOptions } from "mongoose";
import { BankOriginType } from "requestBody";
import events from "../event-handlers/events";
import logger from "../external-services/loggerService";
import { ProviderTypeV1, ProviderTypeV2, TruelayerPaymentsClient } from "../external-services/truelayerService";
import { BankAccountStatusType } from "../external-services/wealthkernelService";
import eventEmitter from "../loaders/eventEmitter";
import { BadRequestError, InternalServerError, NotFoundError } from "../models/ApiErrors";
import { SavingsTopUpAutomationDocument, TopUpAutomationDocument } from "../models/Automation";
import {
  BankAccount,
  BankAccountDocument,
  BankAccountDTOInterface,
  BankAccountInterface,
  BankAccountPopulationFieldsEnum,
  DeactivationReasonEnum
} from "../models/BankAccount";
import { banksConfig, countriesConfig, entitiesConfig } from "@wealthyhood/shared-configs";
import { GiftDocument } from "../models/Gift";
import { MandateDocument } from "../models/Mandate";
import { RewardDocument } from "../models/Reward";
import { UserDocument } from "../models/User";
import DateUtil from "../utils/dateUtil";
import DbUtil from "../utils/dbUtil";
import AutomationService from "./automationService";
import GiftService from "./giftService";
import RewardService from "./rewardService";
import SubscriptionService from "./subscriptionService";
import UserService from "./userService";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import { ProviderEnum } from "../configs/providersConfig";
import BanksUtil from "../utils/banksUtil";
import { GoCardlessPaymentsService } from "../external-services/goCardlessPaymentsService";
import { envIsProd } from "../utils/environmentUtil";
import BankAccountNameChecker, { BankAccountNameCheckResultEnum } from "../lib/bankAccountNameChecker";

const BANK_ORIGIN_MAPPING: Record<BankOriginType, string> = {
  bank_transfer: "bank transfer",
  modal: "modal",
  my_account: "my account",
  truelayer_data: "truelayer data"
};

type AvailableBankType = {
  id: string;
  name: string;
  logo: string;
};

export interface LinkedBankAccountInterface extends Omit<BankAccountInterface, "active" | "activeProviders"> {
  id: string;
  provider?: ProviderTypeV1 | ProviderTypeV2;
}

export default class BankAccountService {
  private static readonly _truelayerPaymentsClient = new TruelayerPaymentsClient();

  public static async createAllWkBankAccounts(): Promise<void> {
    const bankAccountsPendingWkEntry = await BankAccount.find({
      activeProviders: ProviderEnum.WEALTHKERNEL,
      $or: [
        { "providers.wealthkernel.id": { $exists: false } },
        { "providers.wealthkernel.id": { $eq: undefined } }
      ]
    }).populate("owner");

    const bankAccountPromises = bankAccountsPendingWkEntry
      .filter(
        ({ owner, createdAt }) =>
          (owner as UserDocument)?.providers?.wealthkernel?.id &&
          (!createdAt || createdAt < DateUtil.getDateOfMinutesAgo(10))
      )
      .map(BankAccountService._createOrSyncWealthkernelBankAccount);

    await Promise.all(bankAccountPromises);
  }

  /**
   * This method syncs WK bank accounts (created at least 10 minutes ago) as a fallback mechanism to our webhooks.
   */
  public static async syncAllWkBankAccounts(): Promise<void> {
    const bankAccountsPendingWkStatus = await BankAccount.find({
      "providers.wealthkernel.id": { $exists: true, $ne: null },
      createdAt: { $lte: DateUtil.getDateOfMinutesAgo(10) },
      $or: [
        { "providers.wealthkernel.status": { $exists: false } },
        { "providers.wealthkernel.status": { $eq: undefined } },
        { "providers.wealthkernel.status": "Pending" }
      ]
    });
    const bankAccountPromises = bankAccountsPendingWkStatus.map(BankAccountService._syncWkStatusSafely);
    const updatedBankAccounts = await Promise.all(bankAccountPromises);

    await Promise.all(
      updatedBankAccounts.map(async (bankAccount) => {
        if (bankAccount) {
          return BankAccountService._notifyStagnant(bankAccount);
        }
      })
    );
  }

  /**
   * Creates or partially updates a bank account at DB and wealthkernel.
   * Before creation bank account is validated through Fetchify api (only for prod environment)
   *
   * @param owner
   * @param bankAccountData
   * @param bankLinkedFrom
   */
  public static async createOrUpdateBankAccount(
    owner: string,
    bankAccountData: Omit<BankAccountDTOInterface, "activeProviders"> & { truelayerProviderId: string },
    bankLinkedFrom: BankOriginType
  ): Promise<LinkedBankAccountInterface> {
    logger.info(`Creating or updating bank account for user ${owner}`, {
      module: "BankAccountService",
      method: "createOrUpdateBankAccount",
      data: { bankAccountData, bankLinkedFrom }
    });

    const existingBankAccounts = await BankAccount.find({ owner });
    const currentBankAccountExists = existingBankAccounts.find(
      (account) => account.sortCode == bankAccountData.sortCode && account.number == bankAccountData.number
    );

    const active = bankAccountData.active ?? true;

    // If active is false, we want to deactivate (remove) the linked bank account. Otherwise, we want to create a new
    // bank account or activate an existing bank account document.
    const bankAccount = !active
      ? await BankAccountService._removeLinkedBankAccount(currentBankAccountExists, existingBankAccounts)
      : await BankAccountService._createOrUpdateLinkedBankAccount(
          owner,
          currentBankAccountExists,
          bankAccountData,
          bankLinkedFrom,
          existingBankAccounts
        );

    // Populate truelayer provider
    const providersResponse = await BankAccountService._truelayerPaymentsClient.getProviders();
    const provider = providersResponse.find(
      (provider) => provider.provider_id === bankAccountData.truelayerProviderId
    );

    return (({ id, name, number, sortCode, truelayerProviderId, owner }) => ({
      id,
      name,
      number,
      sortCode,
      truelayerProviderId,
      owner,
      provider
    }))(bankAccount);
  }

  public static async createBankAccount(
    bankAccountData: BankAccountDTOInterface,
    user: UserDocument,
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<BankAccountDocument> {
    const existingBankAccount = await BankAccount.findOne({
      owner: bankAccountData.owner,
      iban: bankAccountData.iban
    });

    if (existingBankAccount) {
      return BankAccountService._activateBankAccount(existingBankAccount);
    }

    // GoCardless does not support GB IBANs for direct debits
    if (
      bankAccountData.iban?.startsWith("GB") &&
      bankAccountData.activeProviders?.includes(ProviderEnum.GOCARDLESS)
    ) {
      bankAccountData.activeProviders = bankAccountData.activeProviders.filter(
        (provider) => provider !== ProviderEnum.GOCARDLESS
      );
    }

    const bankAccount = await new BankAccount(bankAccountData).save({ session: options?.session });

    await Promise.all([
      BankAccountService._createGoCardlessEntryIfEligible(bankAccount, { session: options?.session }),
      BankAccountService._performWealthyhoodNameCheckIfEligible(bankAccount, user, { session: options?.session })
    ]);

    return bankAccount;
  }

  /**
   * In case the bank account has a GoCardless active provider, we want to immediately create it in GoCardless
   * so that the user can immediately use it for easy bank transfers.
   * @param bankAccount
   * @param options
   * @private
   */
  private static async _createGoCardlessEntryIfEligible(
    bankAccount: BankAccountDocument,
    options?: {
      session?: mongoose.ClientSession;
    }
  ): Promise<void> {
    if (bankAccount.activeProviders?.includes(ProviderEnum.GOCARDLESS) && !bankAccount.providers?.gocardless?.id) {
      await BankAccountService.createGCEntry(bankAccount, { session: options?.session });
    }
  }

  private static async _performWealthyhoodNameCheckIfEligible(
    bankAccount: BankAccountDocument,
    user: UserDocument,
    options?: {
      session?: mongoose.ClientSession;
    }
  ): Promise<void> {
    if (bankAccount.activeProviders?.includes(ProviderEnum.WEALTHYHOOD)) {
      const decision = await BankAccountNameChecker.checkBankAccountName(bankAccount.name, user.fullName);

      // If the AI thinks this is a good match, we set the bank account as active. Otherwise, we leave it as pending.
      const updatedBankAccount = await BankAccount.findByIdAndUpdate(
        bankAccount.id,
        {
          "providers.wealthyhood.status": decision === BankAccountNameCheckResultEnum.MATCH ? "Active" : "Pending"
        },
        { session: options?.session, new: true }
      );

      await BankAccountService._notifyStagnant(updatedBankAccount);
    }
  }

  public static async deactivateBankAccount(existingBankAccounts: BankAccountDocument[], bankAccountId: string) {
    const currentBankAccount = existingBankAccounts.find((account) => account.id == bankAccountId);
    if (currentBankAccount) {
      await BankAccountService._removeLinkedBankAccount(currentBankAccount, existingBankAccounts);
    } else {
      throw new InternalServerError("Current bank account does not belong to user");
    }
  }

  /**
   * Gets user's linked bank accounts.
   *
   * @param user
   */
  public static async getLinkedBankAccounts(user: UserDocument): Promise<LinkedBankAccountInterface[]> {
    const bankAccounts = await BankAccount.find({ owner: user.id }).populate("mandate");

    const activeBankAccounts = bankAccounts
      .filter((account) => account.active == null || account.active)
      .map((bankAccount) => BankAccountService.fillClientDisplayFields(bankAccount));

    if (activeBankAccounts?.length == 0) {
      return [];
    }

    // Temporarily, we need to include provider in UK bank accounts as they are used by mobile clients.
    if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE) {
      return activeBankAccounts;
    } else if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      const providersResponse = await BankAccountService._truelayerPaymentsClient.getProviders();

      return activeBankAccounts
        .map((bankAccount) => {
          const provider = providersResponse.find((provider) => {
            if (envIsProd()) {
              return provider.provider_id === bankAccount.truelayerProviderId;
            } else {
              // In truelayer sandbox, the data api returns different providers than the payments api
              // and that results in conflict in the flow, so we just accept any stored providers.
              return true;
            }
          });

          return {
            ...bankAccount,
            provider
          };
        })
        .filter((bankAccount) => bankAccount.provider != null);
    }
  }

  /**
   * Gets user's all the bank accounts with suspended status in WK
   */
  public static async getSuspendedBankAccounts(): Promise<BankAccountDocument[]> {
    return BankAccount.find({
      $or: [{ "providers.wealthkernel.status": "Suspended" }, { "providers.wealthyhood.status": "Suspended" }]
    }).populate("owner");
  }

  /**
   * Gets user's all the bank accounts with pending status in WK
   */
  public static async getPendingBankAccounts(): Promise<BankAccountDocument[]> {
    return BankAccount.find({
      $or: [{ "providers.wealthkernel.status": "Pending" }, { "providers.wealthyhood.status": "Pending" }],
      createdAt: { $lte: DateUtil.getDateOfMinutesAgo(15) },
      deactivationReason: { $ne: DeactivationReasonEnum.BANK_NAME_MISMATCH }
    }).populate("owner");
  }

  /**
   * Gets user's bank accounts.
   *
   * @param filter
   * @param sort
   */
  public static async getBankAccounts(filter?: BankAccountsFilter, sort?: string): Promise<BankAccountDocument[]> {
    const dbFilter = this._createBankAccountsDbFilter(filter);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    return BankAccount.find(dbFilter, null, options);
  }

  /**
   * Gets bank account by IBAN and user.
   *
   * @param iban
   */
  public static async getBankAccountByIBAN(iban: string, user: UserDocument): Promise<BankAccountDocument> {
    return BankAccount.findOne({ iban, owner: user.id });
  }

  public static async updateBankAccountStatusByWkBankAccountId(
    wkBankAccountId: string,
    newBankAccountStatus: BankAccountStatusType
  ): Promise<void> {
    const bankAccount = await BankAccount.findOne({
      "providers.wealthkernel.id": wkBankAccountId
    });

    if (!bankAccount) {
      throw new NotFoundError(`Did not found a bankAccount by wealthkernel id ${wkBankAccountId}`);
    } else if (bankAccount.providers.wealthkernel.status !== "Pending") {
      logger.warn(
        `Received ${newBankAccountStatus} status from webhook while bankAccount ${bankAccount.id} has ${bankAccount.providers.wealthkernel.status} status`,
        {
          module: "BankAccountService",
          method: "updateBankAccountStatusByWkBankAccountId",
          data: {
            wkBankAccountId,
            newBankAccountStatus
          }
        }
      );
    }

    if (newBankAccountStatus === "Suspended") {
      logger.error(`BankAccount ${bankAccount._id} got suspended`, {
        module: "BankAccountService",
        method: "updateBankAccountStatusByWkBankAccountId",
        data: {
          wkBankAccountId
        }
      });
    }

    await BankAccount.findOneAndUpdate(
      { "providers.wealthkernel.id": wkBankAccountId },
      { "providers.wealthkernel.status": newBankAccountStatus },
      {
        new: true
      }
    );
  }

  public static getAvailableBanks(
    companyEntity: entitiesConfig.CompanyEntityEnum,
    residencyCountry: countriesConfig.CountryCodesType,
    scope: banksConfig.BankProviderScopeEnum
  ): AvailableBankType[] {
    switch (scope) {
      case banksConfig.BankProviderScopeEnum.DATA:
        return ProviderService.getOpenBankingDataService(companyEntity).getAvailableBanks(residencyCountry);
      case banksConfig.BankProviderScopeEnum.PAY:
        return ProviderService.getOpenBankingPaymentService(companyEntity).getAvailableBanks(residencyCountry);
      default:
        throw new Error(`Scope ${scope} not allowed!`);
    }
  }

  /**
   * Enhances a bank account object with properties useful for the clients.
   * @public
   */
  public static fillClientDisplayFields(bankAccount: BankAccountDocument): BankAccountInterface & { id: string } {
    return {
      ...bankAccount.toObject()
    };
  }

  public static async createGCEntry(
    bankAccount: BankAccountDocument,
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<BankAccountDocument> {
    try {
      logger.info(`Attempting to create GoCardless entry for bank account ${bankAccount.id}`, {
        module: "BankAccountService",
        method: "createGCEntry",
        data: { bankAccount: bankAccount.id }
      });

      const bankAccountGoCardlessID = bankAccount?.providers?.gocardless?.id;
      if (bankAccountGoCardlessID) {
        logger.error(
          `Attempted to submit bank account to GoCardless but already has GC id ${bankAccount.providers?.gocardless?.id} - aborting`,
          { module: "BankAccountService", method: "createGCEntry", data: { bankAccount: bankAccount.id } }
        );
        return;
      }

      await DbUtil.populateIfNotAlreadyPopulated(bankAccount, BankAccountPopulationFieldsEnum.OWNER, {
        session: options?.session
      });
      const user = bankAccount.owner as UserDocument;

      const bankAccountData = {
        account_holder_name: bankAccount.name !== "" ? bankAccount.name : user.fullName,
        iban: bankAccount.iban,
        metadata: {
          wealthyhoodId: bankAccount.id
        },
        links: {
          customer: user.providers.gocardless.id
        }
      };
      const response = await GoCardlessPaymentsService.Instance.createCustomerBankAccount(bankAccountData);

      return BankAccount.findByIdAndUpdate(
        bankAccount.id,
        {
          "providers.gocardless": { id: response.id }
        },
        { new: true, session: options?.session }
      );
    } catch (err) {
      captureException(err);
      logger.error(`Creating a GoCardless entry failed for bank account ${bankAccount.id}`, {
        module: "BankAccountService",
        method: "createGCEntry",
        data: { bankAccount: bankAccount.id, error: JSON.stringify(err) }
      });

      return bankAccount;
    }
  }

  private static _activateBankAccount(bankAccount: BankAccountDocument): Promise<BankAccountDocument> {
    return BankAccount.findByIdAndUpdate(bankAccount.id, { active: true }, { new: true });
  }

  private static _createBankAccountsDbFilter(filter: BankAccountsFilter) {
    const dbFilter = {
      owner: filter.owner
    };

    return dbFilter
      ? Object.fromEntries(
          Object.entries(dbFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }

  private static async _removeLinkedBankAccount(
    bankAccountToRemove: BankAccountDocument,
    existingBankAccounts: BankAccountDocument[]
  ): Promise<BankAccountDocument> {
    const owner = bankAccountToRemove.owner.toString();
    const currentSubscription = await SubscriptionService.getSubscription(owner, { mandate: true });
    const subscriptionMandate = currentSubscription?.mandate as MandateDocument;

    // We retrieve automations that include direct debit payments to make sure the bank account to remove
    // can actually be removed.
    const automations = (
      await AutomationService.getAutomations(
        {
          owner,
          categories: ["TopUpAutomation", "SavingsTopUpAutomation"]
        },
        { mandate: true, owner: false }
      )
    ).data as (TopUpAutomationDocument | SavingsTopUpAutomationDocument)[];

    const subscriptionBankAccountId =
      subscriptionMandate?.bankAccount?._id?.toString() ?? subscriptionMandate?.bankAccount;

    // It does not exist a flow to create an inactive account , so if only one
    // active account remains and is going to be deactivated throw an error
    if (existingBankAccounts.filter(({ active }) => active).length == 1) {
      throw new InternalServerError("At least one active bank account should exist");
    } else if (
      currentSubscription?.category === "DirectDebitSubscription" &&
      subscriptionBankAccountId === bankAccountToRemove.id
    ) {
      throw new InternalServerError("User is currently subscribed with that bank account!");
    } else if (
      automations.some(
        (automation) =>
          automation.status !== "Inactive" &&
          (automation.mandate as MandateDocument).bankAccount.toString() === bankAccountToRemove.id
      )
    ) {
      throw new InternalServerError("User has a recurring top-up with that bank account!");
    }

    const updatedBankAccount = await BankAccount.findByIdAndUpdate(
      bankAccountToRemove._id,
      { active: false },
      {
        runValidators: true,
        setDefaultsOnInsert: true,
        upsert: true,
        new: true
      }
    ).populate("owner");

    const user = updatedBankAccount.owner as UserDocument;

    // When emitting the event, we want to include all active bank accounts after the bank account has been removed
    const activeBanks = existingBankAccounts
      .filter((bankAccount) => bankAccount.active === true)
      .filter((bankAccount) => bankAccount.id !== updatedBankAccount.id)
      .map((bankAccount) => bankAccount.truelayerProviderId);

    eventEmitter.emit(events.user.bankAccountRemoval.eventId, user, {
      truelayerId: updatedBankAccount.truelayerProviderId,
      banks: activeBanks
    });

    return updatedBankAccount;
  }

  private static async _createOrUpdateLinkedBankAccount(
    owner: string,
    currentBankAccountExists: BankAccountDocument,
    bankAccountData: Omit<BankAccountDTOInterface, "activeProviders"> & { truelayerProviderId: string },
    bankLinkedFrom: BankOriginType,
    existingBankAccounts: BankAccountDocument[]
  ): Promise<BankAccountDocument> {
    const bankAccountExists = existingBankAccounts.length > 0;
    const user = await UserService.getUser(owner);

    if (
      !currentBankAccountExists &&
      !BanksUtil.getTruelayerAvailableProviders(user.residencyCountry).includes(
        BanksUtil.getBankFromTruelayerProviderId(bankAccountData.truelayerProviderId)
      )
    ) {
      throw new BadRequestError(`Provider ${bankAccountData.truelayerProviderId} is not supported`);
    }

    const { sortCode, number, name } = bankAccountData;

    const sanitizedBankAccountName = BankAccountService._sanitizeBankAccountName(name);
    const bankAccount = await BankAccount.findOneAndUpdate(
      { owner, sortCode, number },
      {
        owner,
        ...bankAccountData,
        active: true,
        name: sanitizedBankAccountName,
        activeProviders: ProviderService.getProviders(user.companyEntity, [
          ProviderScopeEnum.SINGLE_DEPOSIT_PAYMENTS,
          ProviderScopeEnum.DIRECT_DEBIT_DEPOSIT_PAYMENTS
        ]),
        "providers.truelayer.bankId": bankAccountData.truelayerProviderId
      },
      {
        runValidators: true,
        setDefaultsOnInsert: true,
        upsert: true,
        new: true
      }
    );

    if (!currentBankAccountExists) {
      logger.info("Bank account document created successfully!", {
        module: "BankAccountService",
        method: "_createOrUpdateLinkedBankAccount",
        userEmail: user.email,
        data: {
          userId: user.id,
          bankAccountId: bankAccount.id
        }
      });
    }

    // If the user has accepted rewards, we include that in the event emission as it will determine whether we
    // emit the bankAccountLinked event to Mailchimp or not.
    const settledRewards = (
      (await RewardService.getRewards({ targetUser: user.id, status: "Settled" })) as { data: RewardDocument[] }
    ).data;

    // If the user has accepted any gift and has used it, we include that in the event emission as it will determine whether we
    // emit the bankAccountLinked event to Mailchimp or not.
    const gifts = (await GiftService.getGifts({ targetUserEmail: user.email })) as { data: GiftDocument[] };
    const hasUsedGift = gifts.data.some((gift) => gift.used === true);

    // When emitting the event, we want to include all active bank accounts after the new bank account has been created
    const activeBanks = existingBankAccounts
      .filter((bankAccount) => bankAccount.active === true)
      .map((bankAccount) => bankAccount.truelayerProviderId)
      .concat(bankAccount.truelayerProviderId);

    // Note that even if a request is made with same bank account details, the event will be fired
    eventEmitter.emit(events.user.bankAccountLinking.eventId, user, {
      bankLinkedFrom: BANK_ORIGIN_MAPPING[bankLinkedFrom],
      isFirst: !bankAccountExists,
      hasSettledRewards: settledRewards.length > 0,
      truelayerId: bankAccount.truelayerProviderId,
      banks: activeBanks,
      hasUsedGift
    });

    await BankAccountService._createOrSyncWealthkernelBankAccount(bankAccount);

    return bankAccount;
  }

  private static async _createOrSyncWealthkernelBankAccount(bankAccount: BankAccountDocument): Promise<void> {
    try {
      if (!bankAccount.activeProviders.includes(ProviderEnum.WEALTHKERNEL)) {
        return;
      }

      logger.info(`Attempting to create Wealthkernel entry for bank account ${bankAccount.id}`, {
        module: "BankAccountService",
        method: "_createOrSyncWealthkernelBankAccount",
        data: { bankAccount: bankAccount.id }
      });

      const bankAccountWkID = bankAccount?.providers?.wealthkernel?.id;
      if (bankAccountWkID) {
        logger.error(
          `Attempted to submit bank account to Wealthkernel but already has WK id ${bankAccount.providers?.wealthkernel?.id} - aborting`,
          {
            module: "BankAccountService",
            method: "_createOrSyncWealthkernelBankAccount",
            data: { bankAccount: bankAccount.id }
          }
        );
        return;
      }

      await DbUtil.populateIfNotAlreadyPopulated(bankAccount, BankAccountPopulationFieldsEnum.OWNER);
      const user = bankAccount.owner as UserDocument;

      const partyId = user.providers?.wealthkernel?.id;
      if (!partyId) {
        logger.error(
          `Attempted to submit bank account to Wealthkernel for user ${user.email}, but user has no Wealthkernel party`,
          {
            module: "BankAccountService",
            method: "_createOrSyncWealthkernelBankAccount",
            data: { bankAccount: bankAccount.id, user: user.id }
          }
        );
        throw new Error("Bank account owner has no WK Party ID!");
      }

      // 1. Update wealthkernel bank account details if a bank account exists already
      const existingBankAccounts = await ProviderService.getBrokerageService(
        user.companyEntity
      ).retrieveBankAccounts(partyId, bankAccount.sortCode, bankAccount.number);
      const activeExistingBankAccounts = existingBankAccounts.filter(({ status }) => status === "Active");
      if (activeExistingBankAccounts.length > 0) {
        logger.warn(`Wealthkernel bank account for user ${user.email} exists already - syncing`, {
          module: "BankAccountService",
          method: "_createOrSyncWealthkernelBankAccount"
        });

        const wkBankAccount = existingBankAccounts[0];
        await BankAccount.findOneAndUpdate(
          { _id: bankAccount._id },
          {
            "providers.wealthkernel": { id: wkBankAccount.id, status: wkBankAccount.status }
          },
          { runValidators: true, upsert: false }
        );
      }

      // 2. Create wealthkernel bank account
      const bankAccountWkResponse = await ProviderService.getBrokerageService(user.companyEntity).addBankAccount(
        user,
        bankAccount
      );

      // 3. Update bank account document with wealthkernel info
      await BankAccount.findOneAndUpdate(
        { _id: bankAccount.id },
        {
          "providers.wealthkernel": { id: bankAccountWkResponse.id, status: "Pending" }
        }
      );
    } catch (err) {
      captureException(err);
      logger.error(`Creating a WK entry failed for bank account ${bankAccount.id}`, {
        module: "BankAccountService",
        method: "_createOrSyncWealthkernelBankAccount",
        data: { bankAccount: bankAccount.id, error: err }
      });
    }
  }

  /**
   * @description
   * Emits a stagnant bank account event and updates the document to prevent duplicate notifications.
   */
  private static async _notifyStagnant(bankAccount: BankAccountDocument): Promise<void> {
    try {
      if (bankAccount.isProviderPending && !bankAccount.stagnantEventEmitted) {
        await DbUtil.populateIfNotAlreadyPopulated(bankAccount, BankAccountPopulationFieldsEnum.OWNER);

        eventEmitter.emit(events.bankAccount.bankAccountStagnant.eventId, bankAccount.owner);
        await BankAccount.findOneAndUpdate({ _id: bankAccount.id }, { stagnantEventEmitted: true });
      }
    } catch (err) {
      captureException(err);
      logger.error(`Failed to notify for stagnant bank account ${bankAccount.id}`, {
        module: "BankAccountService",
        method: "_notifyStagnant",
        data: { bankAccount: bankAccount.id, error: err }
      });
    }
  }

  private static async _syncWkStatusSafely(bankAccount: BankAccountDocument): Promise<BankAccountDocument> {
    try {
      const bankAccountWkID = bankAccount?.providers?.wealthkernel?.id;

      await DbUtil.populateIfNotAlreadyPopulated(bankAccount, BankAccountPopulationFieldsEnum.OWNER);
      const user = bankAccount.owner as UserDocument;

      if (bankAccountWkID) {
        const bankAccountWkResponse = await ProviderService.getBrokerageService(
          user.companyEntity
        ).retrieveBankAccount(bankAccountWkID);
        const { status } = bankAccountWkResponse;

        if (status === "Suspended") {
          logger.error(`BankAccount ${bankAccount._id} got suspended`, {
            module: "BankAccountService",
            method: "_syncWkStatusSafely",
            data: {
              bankAccountWkID
            }
          });
        }

        return BankAccount.findOneAndUpdate(
          { _id: bankAccount.id },
          { "providers.wealthkernel.status": status },
          { new: true }
        );
      }
    } catch (err) {
      captureException(err);
      logger.error(`Syncing a WK entry failed for bank account ${bankAccount.id}`, {
        module: "BankAccountService",
        method: "syncWkStatusSafely",
        data: { bankAccount: bankAccount.id, error: err }
      });
    }
  }

  private static _sanitizeBankAccountName(accountName: string): string {
    return accountName.split("(")[0];
  }
}
