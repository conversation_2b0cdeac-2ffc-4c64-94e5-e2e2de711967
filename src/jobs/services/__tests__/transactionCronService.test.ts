import Decimal from "decimal.js";
import { faker } from "@faker-js/faker";
import { entitiesConfig, investmentUniverseConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  AssetTransactionDocument,
  CashbackTransaction,
  CashbackTransactionDocument,
  StockSplitTransaction,
  StockSplitTransactionDocument,
  Transaction
} from "./../../../models/Transaction";
import { DepositMethodEnum } from "../../../types/transactions";
import {
  buildAssetTransaction,
  buildBankAccount,
  buildCashbackTransaction,
  buildDailyPortfolioSavingsTicker,
  buildDailySavingsProductTicker,
  buildDepositCashTransaction,
  buildHoldingDTO,
  buildInvestmentProduct,
  buildMandate,
  buildOrder,
  buildPayout,
  buildPortfolio,
  buildSavingsDividend,
  buildSavingsProduct,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildStockSplitCorporateEvent,
  buildStockSplitTransaction,
  buildTopUpAutomation,
  buildUser
} from "../../../tests/utils/generateModels";
import {
  CurrencyEnum,
  TransactionType,
  WealthkernelService
} from "../../../external-services/wealthkernelService";
import {
  AssetTransaction,
  DepositCashTransaction,
  DepositCashTransactionDocument,
  SavingsDividendTransaction,
  SavingsDividendTransactionDocument,
  SavingsTopupTransaction,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransaction,
  SavingsWithdrawalTransactionDocument
} from "../../../models/Transaction";
import { TransactionCronService } from "../transactionCronService";
import { Portfolio, PortfolioDocument } from "../../../models/Portfolio";
import { UserDocument } from "../../../models/User";
import { buildWealthkernelTransactionResponse } from "../../../tests/utils/generateWealthkernel";
import eventEmitter from "../../../loaders/eventEmitter";
import events from "../../../event-handlers/events";
import { Order, OrderDocument } from "../../../models/Order";
import { GoCardlessPaymentsService } from "../../../external-services/goCardlessPaymentsService";
import { ProviderEnum } from "../../../configs/providersConfig";
import { MandateDocument } from "../../../models/Mandate";
import { buildGoCardlessPayment, buildGoCardlessPayoutItem } from "../../../tests/utils/generateGoCardless";
import { DevengoService } from "../../../external-services/devengoService";
import { Payout, PayoutDocument } from "../../../models/Payout";
import { StockSplitCorporateEventDocument } from "../../../models/CorporateEvent";
import DateUtil from "../../../utils/dateUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;

describe("TransactionCronService", () => {
  beforeAll(async () => await connectDb("TransactionCronService"));
  afterAll(async () => await closeDb());

  describe("processPendingTopUpSavingsWithdrawals", () => {
    describe("when a 'PendingTopUp' Savings withdrawal exists and there are sufficient savings holdings", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeAll(async () => {
        const portfolio = await buildPortfolio({
          savings: new Map([["mmf_dist_gbp", { amount: 500, currency: "GBX" }]])
        });

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            status: "PendingTopUp",
            portfolio: portfolio.id,
            consideration: { amount: 400, currency: CurrencyEnum.GBP }
          },
          {},
          false
        );

        await TransactionCronService.processPendingTopUpSavingsWithdrawals();
      });
      afterAll(async () => await clearDb());

      it("should update the status to 'Pending'", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Pending");
      });
    });

    describe("when a partially filled 'PendingTopUp' Savings withdrawal exists and there are sufficient savings holdings", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeAll(async () => {
        const portfolio = await buildPortfolio({
          savings: new Map([["mmf_dist_gbp", { amount: 200, currency: "GBX" }]])
        });

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            status: "PendingTopUp",
            portfolio: portfolio.id,
            consideration: { amount: 400, currency: CurrencyEnum.GBP }
          },
          {
            status: "InternallyFilled",
            activeProviders: [],
            consideration: { amount: 200, currency: CurrencyEnum.GBP }
          }
        );

        await TransactionCronService.processPendingTopUpSavingsWithdrawals();
      });
      afterAll(async () => await clearDb());

      it("should update the status to 'Pending'", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Pending");
      });
    });

    describe("when a 'PendingTopUp' Savings withdrawal exists and there are sufficient savings holdings but are restricted from another withdrawal", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeAll(async () => {
        const portfolio = await buildPortfolio({
          savings: new Map([["mmf_dist_gbp", { amount: 500, currency: "GBX" }]])
        });

        await buildSavingsWithdrawal({
          status: "Pending",
          portfolio: portfolio.id,
          consideration: { amount: 400, currency: CurrencyEnum.GBP }
        });
        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            status: "PendingTopUp",
            portfolio: portfolio.id,
            consideration: { amount: 400, currency: CurrencyEnum.GBP }
          },
          {},
          false
        );

        await TransactionCronService.processPendingTopUpSavingsWithdrawals();
      });
      afterAll(async () => await clearDb());

      it("should not update the status", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("PendingTopUp");
      });
    });

    describe("when 2 'PendingTopUp' Savings withdrawals exist and there are sufficient savings holdings for both", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      let anotherSavingsWithdrawal: SavingsWithdrawalTransactionDocument;

      beforeAll(async () => {
        const portfolio = await buildPortfolio({
          savings: new Map([["mmf_dist_gbp", { amount: 800, currency: "GBX" }]])
        });

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            status: "PendingTopUp",
            portfolio: portfolio.id,
            consideration: { amount: 400, currency: CurrencyEnum.GBP }
          },
          {},
          false
        );
        anotherSavingsWithdrawal = await buildSavingsWithdrawal(
          {
            status: "PendingTopUp",
            portfolio: portfolio.id,
            consideration: { amount: 400, currency: CurrencyEnum.GBP }
          },
          {},
          false
        );

        await TransactionCronService.processPendingTopUpSavingsWithdrawals();
      });
      afterAll(async () => await clearDb());

      it("should update the status to 'Pending' for both withdrawals", async () => {
        const updatedFirstSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);
        const updatedSecondSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(
          anotherSavingsWithdrawal._id
        );

        expect(updatedFirstSavingsWithdrawal?.status).toEqual("Pending");
        expect(updatedSecondSavingsWithdrawal?.status).toEqual("Pending");
      });
    });

    describe("when 2 'PendingTopUp' Savings withdrawals exist and there are sufficient savings holdings for only one", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      let savingsWithdrawalThatWillNotBeProcessed: SavingsWithdrawalTransactionDocument;

      beforeAll(async () => {
        const portfolio = await buildPortfolio({
          savings: new Map([["mmf_dist_gbp", { amount: 450, currency: "GBX" }]])
        });

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            status: "PendingTopUp",
            portfolio: portfolio.id,
            consideration: { amount: 400, currency: CurrencyEnum.GBP }
          },
          {},
          false
        );
        savingsWithdrawalThatWillNotBeProcessed = await buildSavingsWithdrawal(
          {
            status: "PendingTopUp",
            portfolio: portfolio.id,
            consideration: { amount: 500, currency: CurrencyEnum.GBP }
          },
          {},
          false
        );

        await TransactionCronService.processPendingTopUpSavingsWithdrawals();
      });
      afterAll(async () => await clearDb());

      it("should update the status to 'Pending' for one withdrawal", async () => {
        const updatedFirstSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);
        const updatedSavingsWithdrawalThatWillNotBeProcessed = await SavingsWithdrawalTransaction.findById(
          savingsWithdrawalThatWillNotBeProcessed._id
        );

        expect(updatedFirstSavingsWithdrawal?.status).toEqual("Pending");
        expect(updatedSavingsWithdrawalThatWillNotBeProcessed?.status).toEqual("PendingTopUp");
      });
    });
  });

  describe("syncSavingsTopupTransactionsFromWK", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    const INITIAL_SAVINGS_AMOUNT = 100;
    const SAVINGS_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";

    const buildUserAndPortfolio = async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({
        savings: new Map([
          [
            SAVINGS_PRODUCT_ID,
            {
              amount: INITIAL_SAVINGS_AMOUNT,
              currency: "GBX"
            }
          ]
        ])
      });
    };

    describe("when there is a pending savings topup with a Matched order", () => {
      let savingsTopup: SavingsTopupTransactionDocument;
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id
          },
          {
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Matched"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsTopupTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should not settle the savings topup", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);
        expect(updatedSavingsTopup?.status).toEqual("Pending");
        expect(updatedSavingsTopup?.settledAt).toBeUndefined();
      });

      it("should not emit any events", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });

      it("should not update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);
        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });
    });

    describe("when there is a pending savings topup with a Settled order", () => {
      let savingsTopup: SavingsTopupTransactionDocument;
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id
          },
          {
            status: "Settled",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Matched"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsTopupTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should settle the saving topup", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);

        expect(updatedSavingsTopup?.status).toEqual("Settled");
        expect(updatedSavingsTopup?.settledAt).toEqual(TODAY);
      });

      it("should emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: Decimal.div(savingsTopup.consideration.amount, 100).toNumber(),
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT + savingsTopup.consideration.amount,
          currency: "GBX"
        });
      });
    });

    describe("when there is a pending savings topup with 1 Settled order and 1 InternallyFilled order", () => {
      let savingsTopup: SavingsTopupTransactionDocument;
      let matchedOrder: OrderDocument;
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id
          },
          {},
          false
        );
        // Split transaction consideration amount into 2 orders
        const orderAmount = savingsTopup.consideration.amount / 2;
        [matchedOrder] = await Promise.all([
          buildOrder({
            transaction: savingsTopup.id,
            isin: savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"].isin,
            side: "Buy",
            consideration: {
              originalAmount: orderAmount,
              amountSubmitted: orderAmount,
              amount: orderAmount,
              currency: CurrencyEnum.GBP
            },
            status: "Settled",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Matched"
              }
            }
          }),
          buildOrder({
            transaction: savingsTopup.id,
            isin: savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"].isin,
            side: "Buy",
            consideration: {
              originalAmount: orderAmount,
              amountSubmitted: orderAmount,
              amount: orderAmount,
              currency: CurrencyEnum.GBP
            },
            status: "InternallyFilled",
            activeProviders: []
          })
        ]);
        await TransactionCronService.syncSavingsTopupTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should settle the saving topup", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);

        expect(updatedSavingsTopup?.status).toEqual("Settled");
        expect(updatedSavingsTopup?.settledAt).toEqual(TODAY);
      });

      it("should emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: Decimal.div(savingsTopup.consideration.amount, 100).toNumber(),
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should update the portfolio savings ignoring InternallyFilled orders", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT + matchedOrder.consideration.amount,
          currency: "GBX"
        });
      });
    });

    describe("when there is a pending savings topup with a InternallyFilled order that filled completely the transaction amount", () => {
      let savingsTopup: SavingsTopupTransactionDocument;
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id
          },
          {
            status: "InternallyFilled",
            activeProviders: []
          }
        );
        await TransactionCronService.syncSavingsTopupTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should settle the saving topup", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);

        expect(updatedSavingsTopup?.status).toEqual("Settled");
        expect(updatedSavingsTopup?.settledAt).toEqual(TODAY);
      });

      it("should emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: Decimal.div(savingsTopup.consideration.amount, 100).toNumber(),
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });

      it("should not update the portfolio savings because the order is internally filled", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });
    });

    describe("when there is a pending savings topup with a InternallyFilled order that partially fills the transaction amount", () => {
      let savingsTopup: SavingsTopupTransactionDocument;
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: { amount: 200, currency: CurrencyEnum.GBP }
          },
          {
            status: "InternallyFilled",
            activeProviders: [],
            consideration: { amount: 100, currency: CurrencyEnum.GBP }
          }
        );
        await TransactionCronService.syncSavingsTopupTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should not settle the saving topup", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);

        expect(updatedSavingsTopup?.status).toEqual("Pending");
        expect(updatedSavingsTopup?.settledAt).toBeUndefined();
      });

      it("should not emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });

      it("should not update the portfolio savings because not all orders are created yet", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });
    });

    describe("when there is a pending savings topup with a Rejected order", () => {
      let savingsTopup: SavingsTopupTransactionDocument;

      beforeAll(async () => {
        await buildUserAndPortfolio();

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id
          },
          {
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Rejected"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsTopupTransactionsFromWK();
      });
      afterAll(async () => await clearDb());

      it("should not update the status of the saving withdrawal because this wasn't expected", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);

        expect(updatedSavingsTopup?.status).toEqual("Pending");
        expect(updatedSavingsTopup?.settledAt).toBeUndefined();
      });

      it("should not update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });
    });

    describe("when there is a pending savings topup with a Cancelled order", () => {
      let savingsTopup: SavingsTopupTransactionDocument;

      beforeAll(async () => {
        await buildUserAndPortfolio();

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id
          },
          {
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Cancelled"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsTopupTransactionsFromWK();
      });
      afterAll(async () => await clearDb());

      it("should not update the status of the saving withdrawal because this wasn't expected", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);

        expect(updatedSavingsTopup?.status).toEqual("Pending");
        expect(updatedSavingsTopup?.settledAt).toBeUndefined();
      });

      it("should not update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });
    });

    describe("when there is a settled savings topup", () => {
      let savingsTopup: SavingsTopupTransactionDocument;
      const SETTLED_DATE = new Date("2023-12-18");
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            settledAt: SETTLED_DATE
          },
          {
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Matched"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsTopupTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should no re-settle the transaction as it's already settled", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);

        expect(updatedSavingsTopup?.settledAt).toEqual(SETTLED_DATE);
      });

      it("should not emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should not update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });
    });
  });

  describe("syncSavingsWithdrawalsTransactionsFromWK", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    const INITIAL_SAVINGS_AMOUNT = 1000; // cents
    const SAVINGS_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";

    const buildUserAndPortfolio = async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({
        cash: {
          GBP: {
            available: 0,
            reserved: 0,
            settled: 0
          }
        },
        savings: new Map([
          [
            SAVINGS_PRODUCT_ID,
            {
              amount: INITIAL_SAVINGS_AMOUNT,
              currency: "GBX"
            }
          ]
        ])
      });
    };

    describe("when there is a pending savings withdrawal with a Matched order", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      const SAVINGS_WITHDRAWAL_AMOUNT = 500; // cents
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT,
              currency: CurrencyEnum.GBP
            }
          },
          {
            status: "Matched",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Matched"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should not settle the saving withdrawal", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Pending");
        expect(updatedSavingsWithdrawal?.settledAt).toBeUndefined();
      });

      it("should not update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });

      it("should not update the portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.cash.GBP).toMatchObject({
          available: 0,
          settled: 0,
          reserved: 0
        });
      });

      it("should not emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending savings withdrawal equal to half of user's holding with a Settled order", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      const SAVINGS_WITHDRAWAL_AMOUNT = 500; // cents
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT,
              currency: CurrencyEnum.GBP
            }
          },
          {
            status: "Settled",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Matched"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should settle the saving withdrawal", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Settled");
        expect(updatedSavingsWithdrawal?.settledAt).toEqual(TODAY);
      });

      it("should update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT - SAVINGS_WITHDRAWAL_AMOUNT,
          currency: "GBX"
        });
      });

      it("should update the available & settled portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.cash.GBP).toMatchObject({
          available: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
          reserved: 0,
          settled: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber()
        });
      });

      it("should emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: Decimal.div(savingsWithdrawal.consideration.amount, 100).toNumber(),
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there is a pending savings withdrawal equal to all of user's holdings with a Settled order ", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      const TODAY = new Date("2024-12-18");
      const SAVINGS_WITHDRAWAL_AMOUNT = 1000; // cents

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT,
              currency: CurrencyEnum.GBP
            }
          },
          {
            status: "Settled",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Matched"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should settle the saving withdrawal", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Settled");
        expect(updatedSavingsWithdrawal?.settledAt).toEqual(TODAY);
      });

      it("should update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toBeUndefined();
      });

      it("should update the available & settled portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.cash.GBP).toMatchObject(
          expect.objectContaining({
            available: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
            settled: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
            reserved: 0
          })
        );
      });

      it("should emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: Decimal.div(savingsWithdrawal.consideration.amount, 100).toNumber(),
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there is a pending savings withdrawal equal to all of user's holdings with a Settled order ", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      const TODAY = new Date("2024-12-18");
      const SAVINGS_WITHDRAWAL_AMOUNT = 1000; // cents

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT,
              currency: CurrencyEnum.GBP
            }
          },
          {
            status: "Settled",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Matched"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should settle the saving withdrawal", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Settled");
        expect(updatedSavingsWithdrawal?.settledAt).toEqual(TODAY);
      });

      it("should update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toBeUndefined();
      });

      it("should update the available & settled portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.cash.GBP).toMatchObject({
          available: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
          settled: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
          reserved: 0
        });
      });

      it("should emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: Decimal.div(savingsWithdrawal.consideration.amount, 100).toNumber(),
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there is a pending savings withdrawal with 1 Settled order and 1 InternallyFilled order", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      const SAVINGS_WITHDRAWAL_AMOUNT = 1000; // cents
      let matchedOrder: OrderDocument;
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT,
              currency: CurrencyEnum.GBP
            }
          },
          {},
          false
        );

        // Split transaction consideration amount into 2 orders
        const orderAmount = savingsWithdrawal.consideration.amount / 2;
        [matchedOrder] = await Promise.all([
          buildOrder({
            transaction: savingsWithdrawal.id,
            isin: savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"].isin,
            side: "Sell",
            consideration: {
              originalAmount: orderAmount,
              amountSubmitted: orderAmount,
              amount: orderAmount,
              currency: CurrencyEnum.GBP
            },
            status: "Settled",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Matched"
              }
            }
          }),
          buildOrder({
            transaction: savingsWithdrawal.id,
            isin: savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"].isin,
            side: "Sell",
            consideration: {
              originalAmount: orderAmount,
              amountSubmitted: orderAmount,
              amount: orderAmount,
              currency: CurrencyEnum.GBP
            },
            status: "InternallyFilled",
            activeProviders: []
          })
        ]);
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should settle the saving withdrawal", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Settled");
        expect(updatedSavingsWithdrawal?.settledAt).toEqual(TODAY);
      });

      it("should update the portfolio savings ignoring InternallyFilled orders", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT - matchedOrder.consideration.amount,
          currency: "GBX"
        });
      });

      it("should update the portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.cash.GBP).toMatchObject({
          available: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
          settled: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
          reserved: 0
        });
      });

      it("should emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: Decimal.div(savingsWithdrawal.consideration.amount, 100).toNumber(),
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there is a pending savings withdrawal with a InternallyFilled order that filled completely the transaction amount", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      const SAVINGS_WITHDRAWAL_AMOUNT = 1000; // cents
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT,
              currency: CurrencyEnum.GBP
            }
          },
          {
            status: "InternallyFilled",
            activeProviders: []
          }
        );
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should settle the saving withdrawal", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Settled");
        expect(updatedSavingsWithdrawal?.settledAt).toEqual(TODAY);
      });

      it("should not update the portfolio savings because the order is internally filled", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });

      it("should update the portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.cash.GBP).toMatchObject({
          available: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
          settled: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
          reserved: 0
        });
      });

      it("should emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "sell",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: Decimal.div(savingsWithdrawal.consideration.amount, 100).toNumber(),
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there is a pending savings withdrawal with a InternallyFilled order that partially fills the transaction amount", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      const SAVINGS_WITHDRAWAL_AMOUNT = 1000; // cents
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT,
              currency: CurrencyEnum.GBP
            }
          },
          {
            status: "InternallyFilled",
            activeProviders: [],
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT / 2,
              currency: CurrencyEnum.GBP
            }
          }
        );
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should not settle the saving withdrawal", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Pending");
        expect(updatedSavingsWithdrawal?.settledAt).toBeUndefined();
      });

      it("should not update the portfolio savings because not all orders are created yet", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });

      it("should not update the portfolio cash because not all orders are created yet", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.cash.GBP).toMatchObject({
          available: 0,
          settled: 0,
          reserved: 0
        });
      });

      it("should not emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending savings withdrawal with a Rejected order", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      const SAVINGS_WITHDRAWAL_AMOUNT = 1000; // cents
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT,
              currency: CurrencyEnum.GBP
            }
          },
          {
            status: "Rejected",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Rejected"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should not update the status of the saving withdrawal because this wasn't expected", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Pending");
        expect(updatedSavingsWithdrawal?.settledAt).toBeUndefined();
      });

      it("should not update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });
    });

    describe("when there is a pending savings withdrawal with a Cancelled order", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      const SAVINGS_WITHDRAWAL_AMOUNT = 1000; // cents
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT,
              currency: CurrencyEnum.GBP
            }
          },
          {
            status: "Cancelled",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Cancelled"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should not update the status of the saving withdrawal because this wasn't expected", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Pending");
        expect(updatedSavingsWithdrawal?.settledAt).toBeUndefined();
      });

      it("should not update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });
    });

    describe("when there is a settled savings withdrawal", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      const SETTLED_DATE = new Date("2023-12-18");
      const SAVINGS_WITHDRAWAL_AMOUNT = 1000; // cents
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT,
              currency: CurrencyEnum.GBP
            },
            status: "Settled",
            settledAt: SETTLED_DATE
          },
          {
            status: "Cancelled",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Cancelled"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should not re-settle the savings withdrawal as it's already settled", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.settledAt).toEqual(SETTLED_DATE);
      });

      it("should not emit an Investment Succeeded event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.investmentSuccess.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should not update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT,
          currency: "GBX"
        });
      });
    });

    describe("when there is a pending savings withdrawal with a Settled order and syncing runs twice", () => {
      let savingsWithdrawal: SavingsWithdrawalTransactionDocument;
      const TODAY = new Date("2024-12-18");
      const SAVINGS_WITHDRAWAL_AMOUNT = 500; // cents

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        savingsWithdrawal = await buildSavingsWithdrawal(
          {
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: SAVINGS_WITHDRAWAL_AMOUNT,
              currency: CurrencyEnum.GBP
            }
          },
          {
            status: "Settled",
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                submittedAt: new Date(),
                status: "Matched"
              }
            }
          }
        );
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
        await TransactionCronService.syncSavingsWithdrawalsTransactionsFromWK();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should settle the saving withdrawal", async () => {
        const updatedSavingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal._id);

        expect(updatedSavingsWithdrawal?.status).toEqual("Settled");
        expect(updatedSavingsWithdrawal?.settledAt).toEqual(TODAY);
      });

      it("should update the portfolio savings", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.savings?.get(SAVINGS_PRODUCT_ID)).toMatchObject({
          amount: INITIAL_SAVINGS_AMOUNT - SAVINGS_WITHDRAWAL_AMOUNT,
          currency: "GBX"
        });
      });

      it("should update the available & settled portfolio cash", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio._id);

        expect(updatedPortfolio?.cash.GBP).toMatchObject({
          available: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
          settled: Decimal.div(SAVINGS_WITHDRAWAL_AMOUNT, 100).toNumber(),
          reserved: 0
        });
      });
    });
  });

  describe("syncSavingsDividendsLinkedToSavingsTopup", () => {
    describe("when a savings dividend is linked to a Pending savings topup", () => {
      let dividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });

        const pendingSavingsTopup = await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending"
        });
        dividend = await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          linkedSavingsTopup: pendingSavingsTopup.id,
          status: "PendingReinvestment"
        });

        await TransactionCronService.syncSavingsDividendsLinkedToSavingsTopup();
      });
      afterAll(async () => await clearDb());

      it("should not update the status of the dividend because this wasn't expected", async () => {
        const updatedDividend = await SavingsDividendTransaction.findById(dividend._id);
        expect(updatedDividend?.status).toEqual("PendingReinvestment");
      });
    });

    describe("when a savings dividend is linked to a Settled savings topup", () => {
      const TODAY = new Date("2024-12-18");
      let dividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });

        const settledSavingsTopup = await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: TODAY
        });
        dividend = await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          linkedSavingsTopup: settledSavingsTopup.id,
          status: "PendingReinvestment"
        });

        await TransactionCronService.syncSavingsDividendsLinkedToSavingsTopup();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should update the status of the dividend to 'Settled'", async () => {
        const updatedDividend = await SavingsDividendTransaction.findById(dividend._id);
        expect(updatedDividend?.status).toEqual("Settled");
        expect(updatedDividend?.settledAt).toEqual(TODAY);
      });
    });

    describe("when a savings dividend is linked to a Rejected savings topup", () => {
      let dividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });

        const rejectedSavingsTopup = await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Rejected"
        });
        dividend = await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          linkedSavingsTopup: rejectedSavingsTopup.id,
          status: "PendingReinvestment"
        });

        await TransactionCronService.syncSavingsDividendsLinkedToSavingsTopup();
      });
      afterAll(async () => await clearDb());

      it("should not update the status of the dividend because this wasn't expected", async () => {
        const updatedDividend = await SavingsDividendTransaction.findById(dividend._id);
        expect(updatedDividend?.status).toEqual("PendingReinvestment");
      });
    });

    describe("when the cron task runs twice for a valid dividend", () => {
      const TODAY = new Date("2024-12-18");
      const LATER_TODAY = new Date("2024-12-18T06:32:22.468Z"); // The second time the cron task runs
      let dividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });

        const settledSavingsTopup = await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: TODAY
        });
        dividend = await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          linkedSavingsTopup: settledSavingsTopup.id,
          status: "PendingReinvestment"
        });

        await TransactionCronService.syncSavingsDividendsLinkedToSavingsTopup();
        Date.now = jest.fn(() => +LATER_TODAY);
        await TransactionCronService.syncSavingsDividendsLinkedToSavingsTopup();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should update the status of the dividend to 'Settled' and settledAt should match the first cron run", async () => {
        const updatedDividend = await SavingsDividendTransaction.findById(dividend._id);
        expect(updatedDividend?.status).toEqual("Settled");
        expect(updatedDividend?.settledAt).toEqual(TODAY);
      });
    });
  });

  describe("createWealthkernelSavingsDividends", () => {
    const mockWKTransactionResponse = (euTransactions: TransactionType[], ukTransactions: TransactionType[]) => {
      jest
        .spyOn(WealthkernelService.EUInstance, "listTransactions")
        .mockImplementation(async (options, processor): Promise<void> => {
          if (processor) {
            for (let i = 0; i < euTransactions.length; i++) {
              const transaction = euTransactions[i];
              await processor(transaction);
            }
          }
        });
      jest
        .spyOn(WealthkernelService.UKInstance, "listTransactions")
        .mockImplementation(async (options, processor): Promise<void> => {
          if (processor) {
            for (let i = 0; i < ukTransactions.length; i++) {
              const transaction = ukTransactions[i];
              await processor(transaction);
            }
          }
        });
    };

    describe("when there are no WK dividends in last 5 days", () => {
      beforeAll(async () => {
        jest.clearAllMocks();
        mockWKTransactionResponse([], []);
      });

      it("should not create any savings dividends", async () => {
        await TransactionCronService.createWealthkernelSavingsDividends();

        const dividendsCount = await SavingsDividendTransaction.countDocuments();
        expect(dividendsCount).toBe(0);
      });
    });

    describe("when there EU WK dividends in last 5 days exist but have an investment ISIN", () => {
      const WK_PORTFOLIO_ID = faker.string.uuid();
      const WK_DIVIDEND_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.clearAllMocks();
        mockWKTransactionResponse(
          [
            buildWealthkernelTransactionResponse({
              type: "Dividend",
              id: WK_DIVIDEND_ID,
              status: "Settled",
              portfolioId: WK_PORTFOLIO_ID,
              isin: ASSET_CONFIG["equities_apple"]?.isin,
              consideration: {
                amount: 0,
                currency: CurrencyEnum.GBP
              }
            })
          ],
          [
            buildWealthkernelTransactionResponse({
              type: "Dividend",
              id: WK_DIVIDEND_ID,
              status: "Settled",
              portfolioId: WK_PORTFOLIO_ID,
              isin: ASSET_CONFIG["equities_apple"]?.isin,
              consideration: {
                amount: 0,
                currency: CurrencyEnum.GBP
              }
            })
          ]
        );

        await buildPortfolio({ providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } } });
      });
      afterAll(async () => await clearDb());

      it("should not create any savings dividends", async () => {
        await TransactionCronService.createWealthkernelSavingsDividends();

        const dividendsCount = await SavingsDividendTransaction.countDocuments();
        expect(dividendsCount).toBe(0);
      });
    });

    describe("when the consideration amount is zero", () => {
      const WK_PORTFOLIO_ID = faker.string.uuid();
      const WK_DIVIDEND_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.clearAllMocks();
        mockWKTransactionResponse(
          [
            buildWealthkernelTransactionResponse({
              type: "Dividend",
              id: WK_DIVIDEND_ID,
              status: "Settled",
              portfolioId: WK_PORTFOLIO_ID,
              isin: SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"]?.isin,
              consideration: {
                amount: 0,
                currency: CurrencyEnum.GBP
              }
            })
          ],
          [
            buildWealthkernelTransactionResponse({
              type: "Dividend",
              id: WK_DIVIDEND_ID,
              status: "Settled",
              portfolioId: WK_PORTFOLIO_ID,
              isin: SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"]?.isin,
              consideration: {
                amount: 0,
                currency: CurrencyEnum.GBP
              }
            })
          ]
        );

        await buildPortfolio({ providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } } });
      });
      afterAll(async () => await clearDb());

      it("should not create any savings dividends", async () => {
        await TransactionCronService.createWealthkernelSavingsDividends();

        const dividendsCount = await SavingsDividendTransaction.countDocuments();
        expect(dividendsCount).toBe(0);
      });
    });

    describe("when both EU & UK WK dividends exists in the last 5 days with a savings ISIN", () => {
      const WK_PORTFOLIO_ID = faker.string.uuid();
      const WK_DIVIDEND_ID = faker.string.uuid();
      const DIVIDEND_AMOUNT = 1000; // in Cents
      const WEALTHYHOOD_FEE = 70; // in Cents
      const NET_INTEREST = DIVIDEND_AMOUNT - WEALTHYHOOD_FEE; // in Cents
      const TODAY = new Date("2024-04-02");
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => TODAY.getTime());
        mockWKTransactionResponse(
          [
            buildWealthkernelTransactionResponse({
              type: "Dividend",
              id: WK_DIVIDEND_ID,
              status: "Settled",
              portfolioId: WK_PORTFOLIO_ID,
              isin: SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"]?.isin,
              consideration: {
                amount: DIVIDEND_AMOUNT / 100, // Convert to GBP
                currency: CurrencyEnum.GBP
              }
            })
          ],
          [
            buildWealthkernelTransactionResponse({
              type: "Dividend",
              id: WK_DIVIDEND_ID,
              status: "Settled",
              portfolioId: WK_PORTFOLIO_ID,
              isin: SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"]?.isin,
              consideration: {
                amount: DIVIDEND_AMOUNT / 100, // Convert to GBP
                currency: CurrencyEnum.GBP
              }
            })
          ]
        );

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } }
        });
        const savingsProduct = await buildSavingsProduct(false, { commonId: "mmf_dist_gbp" });
        await buildDailySavingsProductTicker({
          savingsProduct: savingsProduct.id,
          date: new Date("2024-03-29"),
          oneDayYield: 10
        });
        // Split net interest into 2 accruals
        await Promise.all([
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-03-29"),
            dailyAccrual: NET_INTEREST / 2
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-03-31"),
            dailyAccrual: NET_INTEREST / 2
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should create two savings dividends", async () => {
        await TransactionCronService.createWealthkernelSavingsDividends();

        const dividends = await SavingsDividendTransaction.find({ portfolio: portfolio.id });
        expect(dividends).toHaveLength(2);
        expect(dividends).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              owner: user._id,
              portfolio: portfolio._id,
              originalDividendAmount: DIVIDEND_AMOUNT,
              savingsProduct: "mmf_dist_gbp",
              status: "Pending",
              dividendMonth: "2024-03",
              consideration: {
                currency: CurrencyEnum.GBP,
                amount: NET_INTEREST
              },
              fees: expect.objectContaining({
                fx: {
                  amount: 0,
                  currency: CurrencyEnum.GBP
                },
                executionSpread: {
                  amount: 0,
                  currency: CurrencyEnum.GBP
                },
                commission: {
                  amount: WEALTHYHOOD_FEE / 100,
                  currency: CurrencyEnum.GBP
                }
              }),
              providers: expect.objectContaining({
                wealthkernel: {
                  id: WK_DIVIDEND_ID,
                  status: "Settled"
                }
              })
            }),
            expect.objectContaining({
              owner: user._id,
              portfolio: portfolio._id,
              originalDividendAmount: DIVIDEND_AMOUNT,
              savingsProduct: "mmf_dist_gbp",
              status: "Pending",
              dividendMonth: "2024-03",
              consideration: {
                currency: CurrencyEnum.GBP,
                amount: NET_INTEREST
              },
              fees: expect.objectContaining({
                fx: {
                  amount: 0,
                  currency: CurrencyEnum.GBP
                },
                executionSpread: {
                  amount: 0,
                  currency: CurrencyEnum.GBP
                },
                commission: {
                  amount: WEALTHYHOOD_FEE / 100,
                  currency: CurrencyEnum.GBP
                }
              }),
              providers: expect.objectContaining({
                wealthkernel: {
                  id: WK_DIVIDEND_ID,
                  status: "Settled"
                }
              })
            })
          ])
        );
      });
    });

    describe("when the cron service is called twice", () => {
      const WK_PORTFOLIO_ID = faker.string.uuid();
      const WK_DIVIDEND_ID = faker.string.uuid();
      const DIVIDEND_AMOUNT = 1000; // in Cents
      const WEALTHYHOOD_FEE = 70; // in Cents
      const NET_INTEREST = DIVIDEND_AMOUNT - WEALTHYHOOD_FEE; // in Cents
      const TODAY = new Date("2024-04-02");
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => TODAY.getTime());
        mockWKTransactionResponse(
          [
            buildWealthkernelTransactionResponse({
              type: "Dividend",
              id: WK_DIVIDEND_ID,
              status: "Settled",
              portfolioId: WK_PORTFOLIO_ID,
              isin: SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"]?.isin,
              consideration: {
                amount: DIVIDEND_AMOUNT / 100, // Convert to GBP
                currency: CurrencyEnum.GBP
              }
            })
          ],
          []
        );

        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } }
        });
        const savingsProduct = await buildSavingsProduct(false, { commonId: "mmf_dist_gbp" });
        await buildDailySavingsProductTicker({
          savingsProduct: savingsProduct.id,
          date: new Date("2024-03-29"),
          oneDayYield: 10
        });
        // Split net interest into 2 accruals
        await Promise.all([
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-03-29"),
            dailyAccrual: NET_INTEREST / 2
          }),
          buildDailyPortfolioSavingsTicker({
            portfolio: portfolio.id,
            savingsProduct: savingsProduct.id,
            date: new Date("2024-03-31"),
            dailyAccrual: NET_INTEREST / 2
          })
        ]);

        await TransactionCronService.createWealthkernelSavingsDividends();
      });
      afterAll(async () => await clearDb());

      it("should create a savings dividends", async () => {
        const dividends = await SavingsDividendTransaction.find({ portfolio: portfolio.id });
        expect(dividends).toHaveLength(1);
        expect(dividends[0].toObject()).toMatchObject({
          owner: user._id,
          portfolio: portfolio._id,
          originalDividendAmount: DIVIDEND_AMOUNT,
          savingsProduct: "mmf_dist_gbp",
          status: "Pending",
          dividendMonth: "2024-03",
          consideration: {
            currency: CurrencyEnum.GBP,
            amount: NET_INTEREST
          },
          fees: {
            fx: {
              amount: 0,
              currency: CurrencyEnum.GBP
            },
            executionSpread: {
              amount: 0,
              currency: CurrencyEnum.GBP
            },
            commission: {
              amount: WEALTHYHOOD_FEE / 100,
              currency: CurrencyEnum.GBP
            }
          },
          providers: {
            wealthkernel: {
              id: WK_DIVIDEND_ID,
              status: "Settled"
            }
          }
        });
      });

      it("should not create a second dividend when it runs again", async () => {
        await TransactionCronService.createWealthkernelSavingsDividends();

        const dividends = await SavingsDividendTransaction.find({ portfolio: portfolio.id });
        expect(dividends).toHaveLength(1);
      });
    });
  });

  describe("reinvestSavingsDividends", () => {
    describe("when there is a savings dividend with 'Pending' status and no linked savings topup", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let savingsDividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({ owner: user.id });

        savingsDividend = await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id
        });
      });
      afterAll(async () => await clearDb());

      it("should create a savings topup and link it to the dividend", async () => {
        await TransactionCronService.reinvestSavingsDividends();

        const updatedDividend = await SavingsDividendTransaction.findById(savingsDividend._id).populate(
          "linkedSavingsTopup"
        );
        expect(updatedDividend?.status).toBe("PendingReinvestment");
        expect((updatedDividend?.linkedSavingsTopup as SavingsTopupTransactionDocument).toObject()).toMatchObject({
          owner: user._id,
          portfolio: portfolio._id,
          consideration: {
            amount: savingsDividend.consideration.amount,
            currency: CurrencyEnum.GBP
          },
          status: "Pending"
        });
      });
    });

    describe("when there is a savings dividend with 'Pending' status and there are older stagnant savings top-ups", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let savingsDividend: SavingsDividendTransactionDocument;

      // Older savings dividend & linked top-up
      let olderSavingsDividend: SavingsDividendTransactionDocument;
      let olderSavingsTopUp: SavingsTopupTransactionDocument;

      beforeAll(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({ owner: user.id });

        olderSavingsTopUp = await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          consideration: {
            amount: 1,
            currency: "GBP"
          }
        });

        // There is also another savings dividend belonging to another portfolio
        const anotherUser = await buildUser();
        const anotherPortfolio = await buildPortfolio({ owner: anotherUser.id });
        const olderSavingsTopUpBelongingToAnotherPortfolio = await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          consideration: {
            amount: 1,
            currency: "GBP"
          }
        });

        [savingsDividend, olderSavingsDividend] = await Promise.all([
          buildSavingsDividend({
            owner: user.id,
            portfolio: portfolio.id,
            consideration: {
              amount: 1,
              currency: "GBP"
            }
          }),
          // Less than our minimum
          buildSavingsDividend({
            owner: user.id,
            portfolio: portfolio.id,
            status: "PendingReinvestment",
            consideration: {
              amount: 1,
              currency: "GBP"
            },
            linkedSavingsTopup: olderSavingsTopUp.id
          }),
          buildSavingsDividend({
            owner: anotherUser.id,
            portfolio: anotherPortfolio.id,
            status: "PendingReinvestment",
            consideration: {
              amount: 1,
              currency: "GBP"
            },
            linkedSavingsTopup: olderSavingsTopUpBelongingToAnotherPortfolio.id
          })
        ]);
      });
      afterAll(async () => await clearDb());

      it("should create a savings topup with aggregated amount and cancel older dividend", async () => {
        await TransactionCronService.reinvestSavingsDividends();

        const updatedDividend = await SavingsDividendTransaction.findById(savingsDividend._id).populate(
          "linkedSavingsTopup"
        );
        expect(updatedDividend.toObject()).toEqual(
          expect.objectContaining({
            status: "PendingReinvestment",
            consideration: {
              amount: 2,
              currency: "GBP"
            }
          })
        );

        const updatedLinkedSavingsTopUp = updatedDividend?.linkedSavingsTopup as SavingsTopupTransactionDocument;
        expect(updatedLinkedSavingsTopUp).toMatchObject({
          owner: user._id,
          portfolio: portfolio._id,
          consideration: {
            amount: 2,
            currency: CurrencyEnum.GBP
          },
          status: "Pending"
        });

        const [updatedOlderDividend, updatedOlderTopup] = await Promise.all([
          SavingsDividendTransaction.findById(olderSavingsDividend._id),
          SavingsTopupTransaction.findById(olderSavingsTopUp._id)
        ]);
        expect(updatedOlderDividend.status).toBe("Cancelled");
        expect(updatedOlderTopup.status).toBe("Cancelled");
      });
    });

    describe("when there is a pending savings dividend and the cron task runs twice", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let savingsDividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({ owner: user.id });

        savingsDividend = await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id
        });

        await TransactionCronService.reinvestSavingsDividends();
      });
      afterAll(async () => await clearDb());

      it("should create a savings topup and link it to the dividend", async () => {
        const updatedDividend = await SavingsDividendTransaction.findById(savingsDividend._id).populate(
          "linkedSavingsTopup"
        );
        expect(updatedDividend?.status).toBe("PendingReinvestment");
        expect((updatedDividend?.linkedSavingsTopup as SavingsTopupTransactionDocument).toObject()).toMatchObject({
          owner: user._id,
          portfolio: portfolio._id,
          consideration: {
            amount: savingsDividend.consideration.amount,
            currency: CurrencyEnum.GBP
          },
          status: "Pending"
        });
      });

      it("should not create a second topup when it runs again", async () => {
        await TransactionCronService.reinvestSavingsDividends();

        const topupsCount = await SavingsTopupTransaction.countDocuments();
        expect(topupsCount).toBe(1);
      });
    });

    describe("when there is a savings dividend with 'Settled' status and linked to a savings topup", () => {
      let savingsDividend: SavingsDividendTransactionDocument;

      beforeAll(async () => {
        const user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });

        const savingsTopup = await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: new Date()
        });

        savingsDividend = await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          linkedSavingsTopup: savingsTopup.id,
          status: "Settled",
          settledAt: new Date()
        });

        await TransactionCronService.reinvestSavingsDividends();
      });
      afterAll(async () => await clearDb());

      it("should not create a new savings topup", async () => {
        const topupsCount = await SavingsTopupTransaction.countDocuments();
        expect(topupsCount).toBe(1);
      });

      it("should not update the savings dividend status", async () => {
        const updatedDividend = await SavingsDividendTransaction.findById(savingsDividend._id);
        expect(updatedDividend?.status).toBe("Settled");
      });
    });
  });

  describe("processSavingsTopupsPendingDeposit", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    const INITIAL_SAVINGS_AMOUNT = 100;
    const SAVINGS_PRODUCT_ID: savingsUniverseConfig.SavingsProductType = "mmf_dist_gbp";

    const buildUserAndPortfolio = async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({
        owner: user.id,
        savings: new Map([
          [
            SAVINGS_PRODUCT_ID,
            {
              amount: INITIAL_SAVINGS_AMOUNT,
              currency: "GBX"
            }
          ]
        ])
      });
    };

    describe("when there is a pending deposit savings topup linked to a Settled deposit", () => {
      let savingsTopup: SavingsTopupTransactionDocument;
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        const deposit = await buildDepositCashTransaction(
          {
            status: "Settled"
          },
          user,
          portfolio
        );

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            status: "PendingDeposit",
            pendingDeposit: deposit.id
          },
          {},
          false
        );
        await TransactionCronService.processSavingsTopupsPendingDeposit();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should update the status to Pending", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);

        expect(updatedSavingsTopup?.status).toEqual("Pending");
      });

      it("should emit an Investment Created event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          {
            side: "buy",
            category: "savings",
            assetName: "mmf_dist_gbp",
            amount: Decimal.div(savingsTopup.consideration.amount, 100).toNumber(),
            currency: "GBP",
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          }
        );
      });
    });

    describe("when there is a pending deposit savings topup linked to a Rejected deposit", () => {
      let savingsTopup: SavingsTopupTransactionDocument;
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        const deposit = await buildDepositCashTransaction(
          {
            status: "Rejected",
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "failed",
                failureReason: "payment_limit_exceeded"
              }
            }
          },
          user,
          portfolio
        );

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            status: "PendingDeposit",
            pendingDeposit: deposit.id
          },
          {},
          false
        );
        await TransactionCronService.processSavingsTopupsPendingDeposit();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should update the status to DepositFailed", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);

        expect(updatedSavingsTopup?.status).toEqual("DepositFailed");
      });

      it("should not emit an Investment Created event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending deposit savings topup linked to a Cancelled deposit", () => {
      let savingsTopup: SavingsTopupTransactionDocument;
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        const deposit = await buildDepositCashTransaction(
          {
            status: "Cancelled",
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "failed",
                failureReason: "canceled"
              }
            }
          },
          user,
          portfolio
        );

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            status: "PendingDeposit",
            pendingDeposit: deposit.id
          },
          {},
          false
        );
        await TransactionCronService.processSavingsTopupsPendingDeposit();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should update the status to DepositFailed", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);

        expect(updatedSavingsTopup?.status).toEqual("DepositFailed");
      });

      it("should not emit an Investment Created event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending deposit savings topup linked to a Pending deposit", () => {
      let savingsTopup: SavingsTopupTransactionDocument;
      const TODAY = new Date("2024-12-18");

      beforeAll(async () => {
        Date.now = jest.fn(() => +TODAY);
        await buildUserAndPortfolio();

        const deposit = await buildDepositCashTransaction(
          {
            status: "Pending"
          },
          user,
          portfolio
        );

        savingsTopup = await buildSavingsTopup(
          {
            owner: user.id,
            portfolio: portfolio.id,
            status: "PendingDeposit",
            pendingDeposit: deposit.id
          },
          {},
          false
        );
        await TransactionCronService.processSavingsTopupsPendingDeposit();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should not update the status", async () => {
        const updatedSavingsTopup = await SavingsTopupTransaction.findById(savingsTopup._id);

        expect(updatedSavingsTopup?.status).toEqual("PendingDeposit");
      });

      it("should not emit an Investment Created event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });
  });

  describe("createGoCardlessDirectDebitPayments", () => {
    describe("when deposit is not linked to an automation", () => {
      beforeAll(async () => {
        jest.restoreAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createPayment");

        // 1. Create user
        const user = await buildUser(
          {
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          },
          false
        );
        // 2. Create bank account and portfolio
        const portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [],
          cash: { EUR: { available: 0, reserved: 0, settled: 0 } }
        });
        // 3. Create deposit cash transaction
        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.DEVENGO],
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            providers: {}
          }
        });

        await TransactionCronService.createGoCardlessDirectDebitPayments();
      });
      afterAll(async () => await clearDb());

      it("should not hit GoCardless to create a direct-debit payment", async () => {
        expect(GoCardlessPaymentsService.Instance.createPayment).not.toHaveBeenCalled();
      });
    });

    describe("when deposit is linked to an automation that has an inactive mandate", () => {
      beforeAll(async () => {
        jest.restoreAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createPayment");

        // 1. Create user
        const user = await buildUser(
          {
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          },
          false
        );
        // 2. Create bank account and portfolio
        const [bankAccount, portfolio] = await Promise.all([
          buildBankAccount({
            owner: user.id,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          }),
          buildPortfolio({
            owner: user.id,
            holdings: [],
            cash: { EUR: { available: 0, reserved: 0, settled: 0 } }
          })
        ]);
        // 3. Create mandate
        const mandate = await buildMandate({
          owner: user.id,
          isActive: false,
          bankAccount: bankAccount.id,
          category: "Top-Up",
          providers: {
            gocardless: {
              id: faker.string.uuid(),
              status: "expired"
            }
          }
        });
        // 4. Create automation
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });
        // 5. Create deposit cash transaction
        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.DEVENGO],
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            providers: {}
          }
        });

        await TransactionCronService.createGoCardlessDirectDebitPayments();
      });
      afterAll(async () => await clearDb());

      it("should not hit GoCardless to create a direct-debit payment", async () => {
        expect(GoCardlessPaymentsService.Instance.createPayment).not.toHaveBeenCalled();
      });
    });

    describe("when deposit is linked to an inactive automation", () => {
      beforeAll(async () => {
        jest.restoreAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createPayment");

        // 1. Create user
        const user = await buildUser(
          {
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          },
          false
        );
        // 2. Create bank account and portfolio
        const [bankAccount, portfolio] = await Promise.all([
          buildBankAccount({
            owner: user.id,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          }),
          buildPortfolio({
            owner: user.id,
            holdings: [],
            cash: { EUR: { available: 0, reserved: 0, settled: 0 } }
          })
        ]);
        // 3. Create mandate
        const mandate = await buildMandate({
          owner: user.id,
          bankAccount: bankAccount.id,
          category: "Top-Up",
          providers: {
            gocardless: {
              id: faker.string.uuid(),
              status: "active"
            }
          }
        });
        // 4. Create automation
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: false
        });
        // 5. Create deposit cash transaction
        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.DEVENGO],
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            providers: {}
          }
        });

        await TransactionCronService.createGoCardlessDirectDebitPayments();
      });
      afterAll(async () => await clearDb());

      it("should not hit GoCardless to create a direct-debit payment", async () => {
        expect(GoCardlessPaymentsService.Instance.createPayment).not.toHaveBeenCalled();
      });
    });

    describe("when deposit is linked to an automation but has status Cancelled", () => {
      beforeAll(async () => {
        jest.restoreAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createPayment");

        // 1. Create user
        const user = await buildUser(
          {
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          },
          false
        );
        // 2. Create bank account and portfolio
        const [bankAccount, portfolio] = await Promise.all([
          buildBankAccount({
            owner: user.id,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          }),
          buildPortfolio({
            owner: user.id,
            holdings: [],
            cash: { EUR: { available: 0, reserved: 0, settled: 0 } }
          })
        ]);
        // 3. Create mandate
        const mandate = await buildMandate({
          owner: user.id,
          bankAccount: bankAccount.id,
          category: "Top-Up",
          providers: {
            gocardless: {
              id: faker.string.uuid(),
              status: "active"
            }
          }
        });
        // 4. Create automation
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });
        // 5. Create deposit cash transaction
        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.DEVENGO],
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            providers: {}
          },
          status: "Cancelled"
        });

        await TransactionCronService.createGoCardlessDirectDebitPayments();
      });
      afterAll(async () => await clearDb());

      it("should not hit GoCardless to create a direct-debit payment", async () => {
        expect(GoCardlessPaymentsService.Instance.createPayment).not.toHaveBeenCalled();
      });
    });

    describe("when deposit already has a direct debit payment linked to it", () => {
      beforeAll(async () => {
        jest.restoreAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "createPayment");

        // 1. Create user
        const user = await buildUser(
          {
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          },
          false
        );
        // 2. Create bank account and portfolio
        const [bankAccount, portfolio] = await Promise.all([
          buildBankAccount({
            owner: user.id,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          }),
          buildPortfolio({
            owner: user.id,
            holdings: [],
            cash: { EUR: { available: 0, reserved: 0, settled: 0 } }
          })
        ]);
        // 3. Create mandate
        const mandate = await buildMandate({
          owner: user.id,
          bankAccount: bankAccount.id,
          category: "Top-Up",
          providers: {
            gocardless: {
              id: faker.string.uuid(),
              status: "active"
            }
          }
        });
        // 4. Create automation
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });
        // 5. Create deposit cash transaction
        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.DEVENGO],
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            providers: {
              gocardless: {
                id: faker.string.uuid(),
                status: "pending_submission"
              }
            }
          }
        });

        await TransactionCronService.createGoCardlessDirectDebitPayments();
      });
      afterAll(async () => await clearDb());

      it("should not hit GoCardless to create a direct-debit payment", async () => {
        expect(GoCardlessPaymentsService.Instance.createPayment).not.toHaveBeenCalled();
      });
    });

    describe("when deposit is linked to an automation, we have not created a payment for it and deposit does not have a collection request date", () => {
      let user: UserDocument;
      let deposit: DepositCashTransactionDocument;
      let mandate: MandateDocument;

      const GC_MANDATE_ID = faker.string.uuid();
      const GC_PAYMENT_ID = faker.string.uuid();
      const CONSIDERATION_AMOUNT = 1000; // in Cents

      beforeAll(async () => {
        jest.clearAllMocks();
        jest
          .spyOn(GoCardlessPaymentsService.Instance, "retrieveMandate")
          .mockResolvedValue({ id: GC_MANDATE_ID, status: "active", next_possible_charge_date: "2022-01-01" });
        jest.spyOn(GoCardlessPaymentsService.Instance, "createPayment").mockResolvedValue(
          buildGoCardlessPayment({
            id: GC_PAYMENT_ID
          })
        );

        // 1. Create user
        user = await buildUser(
          {
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          },
          false
        );
        // 2. Create bank account and portfolio
        const [bankAccount, portfolio] = await Promise.all([
          buildBankAccount({
            owner: user.id,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          }),
          buildPortfolio({
            owner: user.id,
            holdings: [],
            cash: { EUR: { available: 0, reserved: 0, settled: 0 } }
          })
        ]);
        // 3. Create mandate
        mandate = await buildMandate({
          owner: user.id,
          bankAccount: bankAccount.id,
          category: "Top-Up",
          providers: {
            gocardless: {
              id: GC_MANDATE_ID,
              status: "active"
            }
          }
        });
        // 4. Create automation
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });
        // 5. Create deposit cash transaction
        deposit = await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.DEVENGO],
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            providers: {}
          },
          consideration: {
            amount: CONSIDERATION_AMOUNT,
            currency: CurrencyEnum.EUR
          }
        });

        await TransactionCronService.createGoCardlessDirectDebitPayments();
      });
      afterAll(async () => await clearDb());

      it("should hit GoCardless to create a direct-debit payment", async () => {
        expect(GoCardlessPaymentsService.Instance.createPayment).toHaveBeenCalledWith(
          expect.not.objectContaining({
            charge_date: expect.anything()
          }),
          deposit.id
        );
      });

      it("should update the deposit document with the new direct debit payment ID", async () => {
        const updatedDeposit = await DepositCashTransaction.findById(deposit.id);
        expect(updatedDeposit?.directDebit).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              gocardless: {
                id: GC_PAYMENT_ID,
                status: "pending_submission"
              }
            })
          })
        );
      });
    });

    describe("when deposit is linked to an automation, we have not created a payment for it and next possible charge date is after collection request date", () => {
      let user: UserDocument;
      let deposit: DepositCashTransactionDocument;
      let mandate: MandateDocument;

      const GC_MANDATE_ID = faker.string.uuid();
      const GC_PAYMENT_ID = faker.string.uuid();
      const CONSIDERATION_AMOUNT = 1000; // in Cents

      beforeAll(async () => {
        jest.clearAllMocks();
        jest
          .spyOn(GoCardlessPaymentsService.Instance, "retrieveMandate")
          .mockResolvedValue({ id: GC_MANDATE_ID, status: "active", next_possible_charge_date: "2022-09-03" });
        jest.spyOn(GoCardlessPaymentsService.Instance, "createPayment").mockResolvedValue(
          buildGoCardlessPayment({
            id: GC_PAYMENT_ID
          })
        );

        // 1. Create user
        user = await buildUser(
          {
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          },
          false
        );
        // 2. Create bank account and portfolio
        const [bankAccount, portfolio] = await Promise.all([
          buildBankAccount({
            owner: user.id,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          }),
          buildPortfolio({
            owner: user.id,
            holdings: [],
            cash: { EUR: { available: 0, reserved: 0, settled: 0 } }
          })
        ]);
        // 3. Create mandate
        mandate = await buildMandate({
          owner: user.id,
          bankAccount: bankAccount.id,
          category: "Top-Up",
          providers: {
            gocardless: {
              id: GC_MANDATE_ID,
              status: "active"
            }
          }
        });
        // 4. Create automation
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });
        // 5. Create deposit cash transaction
        deposit = await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.DEVENGO],
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            collectionRequestDate: new Date("2022-09-02T11:00:00Z"),
            providers: {}
          },
          consideration: {
            amount: CONSIDERATION_AMOUNT,
            currency: CurrencyEnum.EUR
          }
        });

        await TransactionCronService.createGoCardlessDirectDebitPayments();
      });
      afterAll(async () => await clearDb());

      it("should hit GoCardless to create a direct-debit payment", async () => {
        expect(GoCardlessPaymentsService.Instance.createPayment).toHaveBeenCalledWith(
          expect.not.objectContaining({
            charge_date: expect.anything()
          }),
          deposit.id
        );
      });

      it("should update the deposit document with the new direct debit payment ID", async () => {
        const updatedDeposit = await DepositCashTransaction.findById(deposit.id);
        expect(updatedDeposit?.directDebit).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              gocardless: {
                id: GC_PAYMENT_ID,
                status: "pending_submission"
              }
            })
          })
        );
      });
    });

    describe("when deposit is linked to an automation, we have not created a payment for it and mandate is active", () => {
      let user: UserDocument;
      let deposit: DepositCashTransactionDocument;
      let mandate: MandateDocument;

      const GC_MANDATE_ID = faker.string.uuid();
      const GC_PAYMENT_ID = faker.string.uuid();
      const CONSIDERATION_AMOUNT = 1000; // in Cents

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "retrieveMandate").mockResolvedValue({
          id: GC_MANDATE_ID,
          status: "active",
          next_possible_charge_date: "2022-09-01"
        });
        jest.spyOn(GoCardlessPaymentsService.Instance, "createPayment").mockResolvedValue(
          buildGoCardlessPayment({
            id: GC_PAYMENT_ID
          })
        );

        // 1. Create user
        user = await buildUser(
          {
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          },
          false
        );
        // 2. Create bank account and portfolio
        const [bankAccount, portfolio] = await Promise.all([
          buildBankAccount({
            owner: user.id,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          }),
          buildPortfolio({
            owner: user.id,
            holdings: [],
            cash: { EUR: { available: 0, reserved: 0, settled: 0 } }
          })
        ]);
        // 3. Create mandate
        mandate = await buildMandate({
          owner: user.id,
          bankAccount: bankAccount.id,
          category: "Top-Up",
          providers: {
            gocardless: {
              id: GC_MANDATE_ID,
              status: "active"
            }
          }
        });
        // 4. Create automation
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });
        // 5. Create deposit cash transaction
        deposit = await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.DEVENGO],
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            collectionRequestDate: new Date("2022-09-02T11:00:00Z"),
            providers: {}
          },
          consideration: {
            amount: CONSIDERATION_AMOUNT,
            currency: CurrencyEnum.EUR
          }
        });

        await TransactionCronService.createGoCardlessDirectDebitPayments();
      });
      afterAll(async () => await clearDb());

      it("should hit GoCardless to create a direct-debit payment", async () => {
        expect(GoCardlessPaymentsService.Instance.createPayment).toHaveBeenCalledWith(
          {
            amount: CONSIDERATION_AMOUNT, // in cents
            currency: "EUR",
            links: {
              mandate: GC_MANDATE_ID
            },
            metadata: {
              wealthyhoodId: deposit.id
            },
            charge_date: "2022-09-02"
          },
          deposit.id
        );
      });

      it("should update the deposit document with the new direct debit payment ID", async () => {
        const updatedDeposit = await DepositCashTransaction.findById(deposit.id);
        expect(updatedDeposit?.directDebit).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              gocardless: {
                id: GC_PAYMENT_ID,
                status: "pending_submission"
              }
            })
          })
        );
      });
    });

    describe("when deposit is linked to an automation, we have not created a payment for it and mandate is pending", () => {
      let user: UserDocument;
      let deposit: DepositCashTransactionDocument;
      let mandate: MandateDocument;

      const GC_MANDATE_ID = faker.string.uuid();
      const GC_PAYMENT_ID = faker.string.uuid();
      const CONSIDERATION_AMOUNT = 1000; // in Cents

      beforeAll(async () => {
        jest.clearAllMocks();
        jest.spyOn(GoCardlessPaymentsService.Instance, "retrieveMandate").mockResolvedValue({
          id: GC_MANDATE_ID,
          status: "active",
          next_possible_charge_date: "2022-09-01"
        });
        jest.spyOn(GoCardlessPaymentsService.Instance, "createPayment").mockResolvedValue(
          buildGoCardlessPayment({
            id: GC_PAYMENT_ID
          })
        );

        // 1. Create user
        user = await buildUser(
          {
            companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          },
          false
        );
        // 2. Create bank account and portfolio
        const [bankAccount, portfolio] = await Promise.all([
          buildBankAccount({
            owner: user.id,
            providers: {
              gocardless: {
                id: faker.string.uuid()
              }
            }
          }),
          buildPortfolio({
            owner: user.id,
            holdings: [],
            cash: { EUR: { available: 0, reserved: 0, settled: 0 } }
          })
        ]);
        // 3. Create mandate
        mandate = await buildMandate({
          owner: user.id,
          bankAccount: bankAccount.id,
          category: "Top-Up",
          providers: {
            gocardless: {
              id: GC_MANDATE_ID,
              status: "pending_submission"
            }
          }
        });
        // 4. Create automation
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });
        // 5. Create deposit cash transaction
        deposit = await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.DEVENGO],
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            collectionRequestDate: new Date("2022-09-02T11:00:00Z"),
            providers: {}
          },
          consideration: {
            amount: CONSIDERATION_AMOUNT,
            currency: CurrencyEnum.EUR
          }
        });

        await TransactionCronService.createGoCardlessDirectDebitPayments();
      });
      afterAll(async () => await clearDb());

      it("should hit GoCardless to create a direct-debit payment", async () => {
        expect(GoCardlessPaymentsService.Instance.createPayment).toHaveBeenCalledWith(
          {
            amount: CONSIDERATION_AMOUNT, // in cents
            currency: "EUR",
            links: {
              mandate: GC_MANDATE_ID
            },
            metadata: {
              wealthyhoodId: deposit.id
            },
            charge_date: "2022-09-02"
          },
          deposit.id
        );
      });

      it("should update the deposit document with the new direct debit payment ID", async () => {
        const updatedDeposit = await DepositCashTransaction.findById(deposit.id);
        expect(updatedDeposit?.directDebit).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              gocardless: {
                id: GC_PAYMENT_ID,
                status: "pending_submission"
              }
            })
          })
        );
      });
    });
  });

  describe("createWealthkernelDirectDebitPayments", () => {
    describe("when deposit is not linked to an automation", () => {
      beforeEach(async () => {
        jest.restoreAllMocks();

        jest.spyOn(WealthkernelService.UKInstance, "createDirectDebitPayment");

        const user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          activeProviders: [],
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {}
          }
        });

        await TransactionCronService.createWealthkernelDirectDebitPayments();
      });

      it("should not hit Wealthkernel to create a direct debit payment", async () => {
        expect(WealthkernelService.UKInstance.createDirectDebitPayment).not.toHaveBeenCalled();
      });
    });

    describe("when deposit is linked to an automation that has a non-active mandate", () => {
      beforeEach(async () => {
        jest.restoreAllMocks();

        jest.spyOn(WealthkernelService.UKInstance, "createDirectDebitPayment");

        const user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          category: "Top-Up",
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true,
          mandate: mandate.id
        });

        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          activeProviders: [],
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {}
          }
        });

        await TransactionCronService.createWealthkernelDirectDebitPayments();
      });

      it("should not hit Wealthkernel to create a direct debit payment", async () => {
        expect(WealthkernelService.UKInstance.createDirectDebitPayment).not.toHaveBeenCalled();
      });
    });

    describe("when deposit is linked to an inactive automation", () => {
      beforeEach(async () => {
        jest.restoreAllMocks();

        jest.spyOn(WealthkernelService.UKInstance, "createDirectDebitPayment");

        const user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          category: "Top-Up",
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: false,
          mandate: mandate.id
        });

        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          activeProviders: [],
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {}
          }
        });

        await TransactionCronService.createWealthkernelDirectDebitPayments();
      });

      it("should not hit Wealthkernel to create a direct debit payment", async () => {
        expect(WealthkernelService.UKInstance.createDirectDebitPayment).not.toHaveBeenCalled();
      });
    });

    describe("when deposit is linked to an automation but has status Cancelled", () => {
      let user: UserDocument;
      let mandate: MandateDocument;

      const WK_PAYMENT_ID = faker.string.uuid();

      beforeEach(async () => {
        jest.restoreAllMocks();

        jest
          .spyOn(WealthkernelService.UKInstance, "createDirectDebitPayment")
          .mockResolvedValue({ id: WK_PAYMENT_ID });

        user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          category: "Top-Up",
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });

        await buildDepositCashTransaction({
          status: "Cancelled",
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          activeProviders: [],
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {}
          }
        });

        await TransactionCronService.createWealthkernelDirectDebitPayments();
      });

      it("should not hit Wealthkernel to create a direct debit payment", async () => {
        expect(WealthkernelService.UKInstance.createDirectDebitPayment).not.toHaveBeenCalled();
      });
    });

    describe("when deposit already has a direct debit payment linked to it", () => {
      beforeEach(async () => {
        jest.restoreAllMocks();

        jest.spyOn(WealthkernelService.UKInstance, "createDirectDebitPayment");

        const user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          activeProviders: [],
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: "some-id",
                status: "Pending"
              }
            }
          }
        });

        await TransactionCronService.createWealthkernelDirectDebitPayments();
      });

      it("should not hit Wealthkernel to create a direct debit payment", async () => {
        expect(WealthkernelService.UKInstance.createDirectDebitPayment).not.toHaveBeenCalled();
      });
    });

    describe("when deposit is linked to an automation, we have not created a payment for it and deposit does not have a collection request date", () => {
      let user: UserDocument;
      let deposit: DepositCashTransactionDocument;
      let mandate: MandateDocument;

      const WK_PAYMENT_ID = faker.string.uuid();

      beforeEach(async () => {
        jest.restoreAllMocks();

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveMandateNextPossiblePaymentDate")
          .mockResolvedValue({ date: "2022-09-03" });
        jest
          .spyOn(WealthkernelService.UKInstance, "createDirectDebitPayment")
          .mockResolvedValue({ id: WK_PAYMENT_ID });

        user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          category: "Top-Up",
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });

        deposit = await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          activeProviders: [],
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {}
          }
        });

        await TransactionCronService.createWealthkernelDirectDebitPayments();
      });

      it("should hit Wealthkernel to create a direct debit payment", async () => {
        expect(WealthkernelService.UKInstance.createDirectDebitPayment).toHaveBeenCalledWith(
          expect.not.objectContaining({
            collectionDate: expect.anything()
          })
        );
      });

      it("should update the deposit document with the new direct debit payment ID", async () => {
        const updatedDeposit = await DepositCashTransaction.findById(deposit.id);
        expect(updatedDeposit.directDebit).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              wealthkernel: {
                id: WK_PAYMENT_ID,
                status: "Pending"
              }
            })
          })
        );
      });
    });

    describe("when deposit is linked to an automation, we have not created a payment for it and next possible charge date is after collection request date", () => {
      let user: UserDocument;
      let deposit: DepositCashTransactionDocument;
      let mandate: MandateDocument;

      const WK_PAYMENT_ID = faker.string.uuid();

      beforeEach(async () => {
        jest.restoreAllMocks();

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveMandateNextPossiblePaymentDate")
          .mockResolvedValue({ date: "2022-09-03" });
        jest
          .spyOn(WealthkernelService.UKInstance, "createDirectDebitPayment")
          .mockResolvedValue({ id: WK_PAYMENT_ID });

        user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          category: "Top-Up",
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });

        deposit = await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          activeProviders: [],
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            collectionRequestDate: new Date("2022-09-02T11:00:00Z"),
            providers: {}
          }
        });

        await TransactionCronService.createWealthkernelDirectDebitPayments();
      });

      it("should hit Wealthkernel to create a direct debit payment", async () => {
        expect(WealthkernelService.UKInstance.createDirectDebitPayment).toHaveBeenCalledWith(
          expect.not.objectContaining({
            collectionDate: expect.anything()
          })
        );
      });

      it("should update the deposit document with the new direct debit payment ID", async () => {
        const updatedDeposit = await DepositCashTransaction.findById(deposit.id);
        expect(updatedDeposit.directDebit).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              wealthkernel: {
                id: WK_PAYMENT_ID,
                status: "Pending"
              }
            })
          })
        );
      });
    });

    describe("when deposit is linked to an automation and we have not created a payment for it", () => {
      let user: UserDocument;
      let deposit: DepositCashTransactionDocument;
      let mandate: MandateDocument;

      const WK_PAYMENT_ID = faker.string.uuid();

      beforeEach(async () => {
        jest.restoreAllMocks();

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveMandateNextPossiblePaymentDate")
          .mockResolvedValue({ date: "2022-09-01" });
        jest
          .spyOn(WealthkernelService.UKInstance, "createDirectDebitPayment")
          .mockResolvedValue({ id: WK_PAYMENT_ID });

        user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        mandate = await buildMandate({
          owner: user.id,
          bankAccount: user.bankAccounts[0].id,
          category: "Top-Up",
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          mandate: mandate.id,
          active: true
        });

        deposit = await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT,
          activeProviders: [],
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            collectionRequestDate: new Date("2022-09-02T11:00:00Z"),
            providers: {}
          }
        });

        await TransactionCronService.createWealthkernelDirectDebitPayments();
      });

      it("should hit Wealthkernel to create a direct debit payment", async () => {
        expect(WealthkernelService.UKInstance.createDirectDebitPayment).toHaveBeenCalledWith({
          amount: {
            amount: Decimal.div(deposit.consideration.amount, 100).toNumber(),
            currency: deposit.consideration.currency
          },
          collectionDate: "2022-09-02",
          mandateId: mandate.providers?.wealthkernel.id,
          portfolioId: "WK-PORTFOLIO-ID"
        });
      });

      it("should update the deposit document with the new direct debit payment ID", async () => {
        const updatedDeposit = await DepositCashTransaction.findById(deposit.id);
        expect(updatedDeposit.directDebit).toEqual(
          expect.objectContaining({
            providers: expect.objectContaining({
              wealthkernel: {
                id: WK_PAYMENT_ID,
                status: "Pending"
              }
            })
          })
        );
      });
    });
  });

  describe("createPaymentsForEligiblePayouts", () => {
    describe("when no payout exists with confirmed incoming payment", () => {
      beforeAll(async () => {
        jest.restoreAllMocks();
        jest.spyOn(DevengoService.Instance, "createPayment");

        await TransactionCronService.createPaymentsForEligiblePayouts();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should not try create a Devengo payment", async () => {
        expect(DevengoService.Instance.createPayment).not.toHaveBeenCalled();
      });
    });

    describe("when a confirmed payout exists and no bank transfer payments are created", () => {
      let payout: PayoutDocument;
      let deposit: DepositCashTransactionDocument;

      const GC_PAYMENT_ID = faker.string.uuid();
      const DEVENGO_OUTGOING_PAYMENT_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.restoreAllMocks();
        jest
          .spyOn(DevengoService.Instance, "createPayment")
          .mockResolvedValue({ payment: { id: DEVENGO_OUTGOING_PAYMENT_ID } });
        jest.spyOn(GoCardlessPaymentsService.Instance, "retrievePayoutItems").mockResolvedValue([
          buildGoCardlessPayoutItem({
            links: {
              payment: GC_PAYMENT_ID
            }
          }), // Valid payout item - matches a deposit transaction
          buildGoCardlessPayoutItem({
            type: "revenue_share"
          }) // Invalid payout item - Will get ignored
        ]);

        payout = await buildPayout({
          providers: {
            devengo: {
              id: faker.string.uuid(),
              status: "confirmed"
            }
          }
        });
        const user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        });
        deposit = await buildDepositCashTransaction({
          owner: user.id,
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          activeProviders: [ProviderEnum.GOCARDLESS, ProviderEnum.WEALTHKERNEL],
          transferWithIntermediary: {
            collection: {
              incomingPayment: {
                providers: {
                  devengo: {
                    id: faker.string.uuid(),
                    status: "confirmed",
                    accountId: process.env.DEVENGO_GOCARDLESS_PAYOUTS_COLLECTION_ACCOUNT_ID
                  }
                }
              }
            }
          },
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            providers: {
              gocardless: {
                id: GC_PAYMENT_ID,
                status: "paid_out"
              }
            }
          }
        });

        await TransactionCronService.createPaymentsForEligiblePayouts();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should not update payout status", async () => {
        const updatedPayout = await Payout.findById(payout.id);
        expect(updatedPayout?.status).toBe("Pending");
      });

      it("should create a Devengo payment and update the deposit transaction", async () => {
        expect(DevengoService.Instance.createPayment).toHaveBeenCalledWith({
          destination: {
            iban: process.env.WEALTHKERNEL_WEALTHYHOOD_EUROPE_IBAN
          },
          amount: {
            cents: deposit.consideration.amount,
            currency: "EUR"
          },
          account_id: process.env.DEVENGO_GOCARDLESS_PAYOUTS_COLLECTION_ACCOUNT_ID,
          company_reference: deposit.id,
          recipient: "Wealthkernel Ltd",
          description: expect.anything()
        });

        const updatedDeposit = await DepositCashTransaction.findById(deposit.id);
        expect(updatedDeposit?.toObject()).toMatchObject({
          transferWithIntermediary: {
            collection: {
              outgoingPayment: {
                providers: {
                  devengo: {
                    id: DEVENGO_OUTGOING_PAYMENT_ID,
                    status: "created"
                  }
                }
              }
            }
          }
        });
      });
    });

    describe("when a confirmed payout exists and all bank transfer payments are created", () => {
      let payout: PayoutDocument;

      const GC_PAYMENT_ID = faker.string.uuid();
      const DEVENGO_OUTGOING_PAYMENT_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.restoreAllMocks();
        jest
          .spyOn(DevengoService.Instance, "createPayment")
          .mockResolvedValue({ payment: { id: DEVENGO_OUTGOING_PAYMENT_ID } });
        jest.spyOn(GoCardlessPaymentsService.Instance, "retrievePayoutItems").mockResolvedValue([
          buildGoCardlessPayoutItem({
            links: {
              payment: GC_PAYMENT_ID
            }
          })
        ]);

        payout = await buildPayout({
          providers: {
            devengo: {
              id: faker.string.uuid(),
              status: "confirmed"
            }
          }
        });
        await buildDepositCashTransaction({
          depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
          activeProviders: [ProviderEnum.GOCARDLESS, ProviderEnum.WEALTHKERNEL],
          transferWithIntermediary: {
            collection: {
              outgoingPayment: {
                providers: {
                  devengo: {
                    id: faker.string.uuid(),
                    status: "created"
                  }
                }
              }
            }
          },
          directDebit: {
            activeProviders: [ProviderEnum.GOCARDLESS],
            providers: {
              gocardless: {
                id: GC_PAYMENT_ID,
                status: "paid_out"
              }
            }
          }
        });

        await TransactionCronService.createPaymentsForEligiblePayouts();
      });
      afterAll(async () => {
        jest.clearAllMocks();
        await clearDb();
      });

      it("should update payout status", async () => {
        const updatedPayout = await Payout.findById(payout.id);
        expect(updatedPayout?.status).toBe("Completed");
      });

      it("should not try create a Devengo payment", async () => {
        expect(DevengoService.Instance.createPayment).not.toHaveBeenCalled();
      });
    });
  });

  describe("processAssetTransactionsPendingDeposit", () => {
    describe("when a pending deposit repeating asset transaction exists and is linked to a Settled deposit", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        user = await buildUser();
        portfolio = await buildPortfolio({ owner: user.id });
        const automation = await buildTopUpAutomation({
          owner: user.id,
          category: "TopUpAutomation",
          active: true
        });
        const deposit = await buildDepositCashTransaction(
          {
            status: "Settled",
            linkedAutomation: automation.id
          },
          user,
          portfolio
        );
        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          pendingDeposit: deposit.id,
          linkedAutomation: automation.id
        });

        await TransactionCronService.processAssetTransactionsPendingDeposit();
      });
      afterAll(async () => await clearDb());

      it("should update the status to Pending", async () => {
        const updatedAssetTransaction = await AssetTransaction.findById(assetTransaction._id);
        expect(updatedAssetTransaction?.status).toEqual("Pending");
      });

      it("should not emit any investment creation events", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.anything(),
          expect.anything()
        );
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when a pending deposit portfolio buy asset transaction exists and is linked to a Settled deposit", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        user = await buildUser();
        portfolio = await buildPortfolio({ owner: user.id });
        const deposit = await buildDepositCashTransaction(
          {
            status: "Settled"
          },
          user,
          portfolio
        );
        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          pendingDeposit: deposit.id
        });

        await TransactionCronService.processAssetTransactionsPendingDeposit();
      });
      afterAll(async () => await clearDb());

      it("should update the status to Pending", async () => {
        const updatedAssetTransaction = await AssetTransaction.findById(assetTransaction._id);
        expect(updatedAssetTransaction?.status).toEqual("Pending");
      });

      it("should emit investment creation events", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({
            isFirst: true,
            currency: "GBP",
            side: "buy",
            category: "portfolio",
            amount: Decimal.div(assetTransaction.consideration.amount, 100).toNumber(),
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          })
        );
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("when a pending deposit asset buy asset transaction exists and is linked to a Settled deposit", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        user = await buildUser();
        portfolio = await buildPortfolio({ owner: user.id });
        const deposit = await buildDepositCashTransaction(
          {
            status: "Settled"
          },
          user,
          portfolio
        );
        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          pendingDeposit: deposit.id,
          portfolioTransactionCategory: "update"
        });

        assetTransaction.orders = [
          await buildOrder({
            status: "Pending",
            side: "Buy",
            transaction: assetTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              amount: 1000,
              currency: "GBP"
            }
          })
        ];

        await assetTransaction.save();

        await TransactionCronService.processAssetTransactionsPendingDeposit();
      });
      afterAll(async () => await clearDb());

      it("should update the status to Pending", async () => {
        const updatedAssetTransaction = await AssetTransaction.findById(assetTransaction._id);
        expect(updatedAssetTransaction?.status).toEqual("Pending");
      });

      it("should emit investment creation events", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({
            isFirst: true,
            currency: "GBP",
            side: "buy",
            category: "etf",
            assetName: "Vanguard S&P 500 UCITS ETF",
            amount: 10,
            cashbackAmount: 0,
            fxFees: 0,
            commissionFees: 0,
            executionSpreadFees: 0,
            frequency: "one-off"
          })
        );
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("when a pending deposit asset transaction exists and is linked to a Rejected deposit", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let assetTransaction: AssetTransactionDocument;
      let order: OrderDocument;
      let cashback: CashbackTransactionDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        user = await buildUser();
        portfolio = await buildPortfolio({ owner: user.id });
        const deposit = await buildDepositCashTransaction(
          {
            status: "Rejected"
          },
          user,
          portfolio
        );
        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          pendingDeposit: deposit.id
        });
        cashback = await buildCashbackTransaction({
          status: "Pending",
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: assetTransaction.id
        });
        order = await buildOrder({
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin
        });
        assetTransaction.orders = [order];
        await assetTransaction.save();

        await TransactionCronService.processAssetTransactionsPendingDeposit();
      });
      afterAll(async () => await clearDb());

      it("should update the status to DepositFailed", async () => {
        const updatedAssetTransaction = await AssetTransaction.findById(assetTransaction._id);
        expect(updatedAssetTransaction?.status).toEqual("DepositFailed");
      });

      it("should cancel the linked cashback", async () => {
        const updatedCashback = await CashbackTransaction.findById(cashback.id);
        expect(updatedCashback?.status).toEqual("Rejected");
      });

      it("should cancel the linked order", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder?.status).toEqual("Cancelled");
      });

      it("should not emit any investment creation events", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.anything(),
          expect.anything()
        );
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when a pending deposit asset transaction exists and is linked to a Cancelled deposit", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let assetTransaction: AssetTransactionDocument;
      let order: OrderDocument;
      let cashback: CashbackTransactionDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        await buildInvestmentProduct(true, { assetId: "equities_apple" });

        user = await buildUser();
        portfolio = await buildPortfolio({ owner: user.id });
        const deposit = await buildDepositCashTransaction(
          {
            status: "Cancelled"
          },
          user,
          portfolio
        );
        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          pendingDeposit: deposit.id
        });
        cashback = await buildCashbackTransaction({
          status: "Pending",
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: assetTransaction.id
        });
        order = await buildOrder({
          transaction: assetTransaction.id,
          isin: ASSET_CONFIG["equities_apple"].isin
        });
        assetTransaction.orders = [order];
        await assetTransaction.save();

        await TransactionCronService.processAssetTransactionsPendingDeposit();
      });
      afterAll(async () => await clearDb());

      it("should update the status to DepositFailed", async () => {
        const updatedAssetTransaction = await AssetTransaction.findById(assetTransaction._id);
        expect(updatedAssetTransaction?.status).toEqual("DepositFailed");
      });

      it("should cancel the linked cashback", async () => {
        const updatedCashback = await CashbackTransaction.findById(cashback.id);
        expect(updatedCashback?.status).toEqual("Rejected");
      });

      it("should cancel the linked order", async () => {
        const updatedOrder = await Order.findById(order.id);
        expect(updatedOrder?.status).toEqual("Cancelled");
      });

      it("should not emit any investment creation events", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.anything(),
          expect.anything()
        );
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when a pending deposit asset transaction exists and is linked to a Pending deposit", () => {
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeAll(async () => {
        jest.clearAllMocks();

        user = await buildUser();
        portfolio = await buildPortfolio({ owner: user.id });
        const deposit = await buildDepositCashTransaction(
          {
            status: "Pending"
          },
          user,
          portfolio
        );
        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          pendingDeposit: deposit.id
        });

        await TransactionCronService.processAssetTransactionsPendingDeposit();
      });
      afterAll(async () => await clearDb());

      it("should not update the status to Pending", async () => {
        const updatedAssetTransaction = await AssetTransaction.findById(assetTransaction._id);
        expect(updatedAssetTransaction?.status).toEqual("PendingDeposit");
      });

      it("should not emit any investment creation events", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.anything(),
          expect.anything()
        );
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.anything(),
          expect.anything()
        );
      });
    });
  });

  describe("createStockSplitTransactions", () => {
    describe("when there are no splits for today", () => {
      const TODAY = new Date("2024-01-10");

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        await TransactionCronService.createStockSplitTransactions();
      });
      afterAll(async () => await clearDb());

      it("should NOT create a stock split transaction", async () => {
        const split = await StockSplitTransaction.findOne();
        expect(split).toBeNull();
      });
    });

    describe("when there is a split for today but no portfolios that hold that asset", () => {
      const TODAY = new Date("2024-01-10");
      const SPLIT_RATIO = "3.000000/1.000000";

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        await buildStockSplitCorporateEvent({
          asset: "equities_sony",
          splitRatio: SPLIT_RATIO,
          date: TODAY
        });

        await TransactionCronService.createStockSplitTransactions();
      });
      afterAll(async () => await clearDb());

      it("should NOT create a stock split transaction", async () => {
        const split = await StockSplitTransaction.findOne();
        expect(split).toBeNull();
      });
    });

    describe("when there is a split for yesterday and a single portfolio holds that asset", () => {
      const TODAY = new Date("2024-01-10");
      const YESTERDAY = DateUtil.getYesterday(TODAY);
      const SPLIT_RATIO = "3.000000/1.000000";

      let portfolio: PortfolioDocument;
      let stockSplit: StockSplitCorporateEventDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        stockSplit = await buildStockSplitCorporateEvent({
          asset: "equities_sony",
          splitRatio: SPLIT_RATIO,
          date: YESTERDAY
        });

        portfolio = await buildPortfolio({
          holdings: await Promise.all([buildHoldingDTO(true, "equities_sony")])
        });

        await TransactionCronService.createStockSplitTransactions();
      });
      afterAll(async () => await clearDb());

      it("should create a stock split transaction", async () => {
        const split = await StockSplitTransaction.findOne();
        expect(split.toObject()).toEqual(
          expect.objectContaining({
            status: "Pending",
            portfolio: portfolio._id,
            stockSplit: stockSplit._id
          })
        );
      });
    });

    describe("when there is a split for today and a single portfolio holds that asset", () => {
      const TODAY = new Date("2024-01-10");
      const SPLIT_RATIO = "3.000000/1.000000";

      let portfolio: PortfolioDocument;
      let stockSplit: StockSplitCorporateEventDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        stockSplit = await buildStockSplitCorporateEvent({
          asset: "equities_sony",
          splitRatio: SPLIT_RATIO,
          date: TODAY
        });

        portfolio = await buildPortfolio({
          holdings: await Promise.all([buildHoldingDTO(true, "equities_sony")])
        });

        await TransactionCronService.createStockSplitTransactions();
      });
      afterAll(async () => await clearDb());

      it("should create a stock split transaction", async () => {
        const split = await StockSplitTransaction.findOne();
        expect(split.toObject()).toEqual(
          expect.objectContaining({
            status: "Pending",
            portfolio: portfolio._id,
            stockSplit: stockSplit._id
          })
        );
      });
    });

    describe("when there is a split for today, a single portfolio holds that asset, and the method runs twice", () => {
      const TODAY = new Date("2024-01-10");
      const SPLIT_RATIO = "3.000000/1.000000";

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        await buildStockSplitCorporateEvent({
          asset: "equities_sony",
          splitRatio: SPLIT_RATIO,
          date: TODAY
        });

        await buildPortfolio({
          holdings: await Promise.all([buildHoldingDTO(true, "equities_sony")])
        });

        await TransactionCronService.createStockSplitTransactions();
        await TransactionCronService.createStockSplitTransactions();
      });
      afterAll(async () => await clearDb());

      it("should only create ONE stock split transaction", async () => {
        const splits = await StockSplitTransaction.find();
        expect(splits.length).toEqual(1);
      });
    });
  });

  describe("processStockSplitTransactions", () => {
    describe("when there is a split for today and a transaction for that split exists", () => {
      const TODAY = new Date("2024-01-10");
      const SPLIT_RATIO = "3.000000/1.000000";

      let portfolio: PortfolioDocument;
      let transaction: StockSplitTransactionDocument;
      let stockSplit: StockSplitCorporateEventDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        stockSplit = await buildStockSplitCorporateEvent({
          asset: "equities_sony",
          splitRatio: SPLIT_RATIO,
          date: TODAY
        });

        portfolio = await buildPortfolio({
          holdings: await Promise.all([buildHoldingDTO(true, "equities_sony", 1)])
        });
        transaction = await buildStockSplitTransaction({
          owner: portfolio.owner,
          portfolio: portfolio.id,
          stockSplit: stockSplit.id
        });

        await TransactionCronService.processStockSplitTransactions();
      });
      afterAll(async () => await clearDb());

      it("should adjust the holdings for portfolios that hold the asset", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio.toObject().holdings).toEqual([{ assetCommonId: "equities_sony", quantity: 3 }]);
      });

      it("should settle the transaction", async () => {
        const updatedTransaction = await Transaction.findById(transaction.id);
        expect(updatedTransaction.toObject().status).toEqual("Settled");
      });
    });

    describe("when there is a split for today, a transaction for that split exists and the method runs twice", () => {
      const TODAY = new Date("2024-01-10");
      const SPLIT_RATIO = "3.000000/1.000000";

      let portfolio: PortfolioDocument;
      let transaction: StockSplitTransactionDocument;
      let stockSplit: StockSplitCorporateEventDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        stockSplit = await buildStockSplitCorporateEvent({
          asset: "equities_sony",
          splitRatio: SPLIT_RATIO,
          date: TODAY
        });

        portfolio = await buildPortfolio({
          holdings: await Promise.all([buildHoldingDTO(true, "equities_sony", 1)])
        });
        transaction = await buildStockSplitTransaction({
          owner: portfolio.owner,
          portfolio: portfolio.id,
          stockSplit: stockSplit.id
        });

        await TransactionCronService.processStockSplitTransactions();
        await TransactionCronService.processStockSplitTransactions();
      });
      afterAll(async () => await clearDb());

      it("should adjust the holdings for portfolios that hold the asset only once", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio.toObject().holdings).toEqual([{ assetCommonId: "equities_sony", quantity: 3 }]);
      });

      it("should settle the transaction", async () => {
        const updatedTransaction = await Transaction.findById(transaction.id);
        expect(updatedTransaction.toObject().status).toEqual("Settled");
      });
    });

    describe("when there is a split for today, a transaction for that split exists and the split ratio results in a more than 4 decimal places quantity", () => {
      const TODAY = new Date("2024-01-10");
      const SPLIT_RATIO = "1.050000/1.000000";

      let portfolio: PortfolioDocument;
      let transaction: StockSplitTransactionDocument;
      let stockSplit: StockSplitCorporateEventDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        jest.spyOn(eventEmitter, "emit");
        Date.now = jest.fn(() => +TODAY);

        stockSplit = await buildStockSplitCorporateEvent({
          asset: "equities_sony",
          splitRatio: SPLIT_RATIO,
          date: TODAY
        });

        portfolio = await buildPortfolio({
          holdings: await Promise.all([buildHoldingDTO(true, "equities_sony", 5.5555)]) // 5.5555 * 1.05 = 5.833275
        });

        transaction = await buildStockSplitTransaction({
          owner: portfolio.owner,
          portfolio: portfolio.id,
          stockSplit: stockSplit.id
        });

        await TransactionCronService.processStockSplitTransactions();
      });
      afterAll(async () => await clearDb());

      it("should create a stock split transaction with the resulting quantity round DOWN to 4 decimal places", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio.toObject().holdings).toEqual([
          { assetCommonId: "equities_sony", quantity: 5.8332 }
        ]);
      });
    });
  });
});
